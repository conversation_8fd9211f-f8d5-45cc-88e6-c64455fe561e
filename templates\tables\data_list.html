{% extends 'base.html' %}
{% load table_tags %}

{% block title %}بيانات {{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">البيانات</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="{{ table.icon }}" style="color: {{ table.color }}"></i>
        بيانات {{ table.name }}
    </h1>

    <div class="btn-group">
        <a href="{% url 'tables:data_create' table.id %}" class="btn btn-success">
            <i class="fas fa-plus"></i> إضافة سجل
        </a>
        <a href="{% url 'tables:data_import' table.id %}" class="btn btn-primary">
            <i class="fas fa-file-import"></i> استيراد
        </a>
        <a href="{% url 'tables:data_export' table.id %}" class="btn btn-secondary">
            <i class="fas fa-file-export"></i> تصدير
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <input type="text" name="q" class="form-control" placeholder="ابحث..." value="{{ search_query }}">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'tables:data_list' table.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-redo"></i> إعادة تعيين
                </a>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        {% for field in fields %}
                        <th>
                            <a href="?sort={{ field.name }}&dir={% if sort_field == field.name and sort_dir == 'asc' %}desc{% else %}asc{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">
                                {{ field.label }}
                                {% if sort_field == field.name %}
                                <i class="fas fa-sort-{% if sort_dir == 'asc' %}up{% else %}down{% endif %}"></i>
                                {% endif %}
                            </a>
                        </th>
                        {% endfor %}
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if records %}
                        {% for record in records %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            {% for field in fields %}
                            <td>
                                {% with field_value=record|getattribute:field.name %}
                                    {% if field_value is not None %}
                                        {% if field.field_type == 'boolean' %}
                                            {% if field_value %}
                                            <span class="badge bg-success"><i class="fas fa-check"></i> نعم</span>
                                            {% else %}
                                            <span class="badge bg-secondary"><i class="fas fa-times"></i> لا</span>
                                            {% endif %}
                                        {% elif field.field_type == 'date' %}
                                            {{ field_value|date:"Y-m-d" }}
                                        {% elif field.field_type == 'time' %}
                                            {{ field_value|time:"H:i" }}
                                        {% elif field.field_type == 'datetime' %}
                                            {{ field_value|date:"Y-m-d H:i" }}
                                        {% elif field.field_type == 'file' or field.field_type == 'image' or field.field_type == 'pdf' or field.field_type == 'document' %}
                                            {% if field_value %}
                                            <a href="{{ field_value.url }}" target="_blank">عرض الملف</a>
                                            {% endif %}
                                        {% else %}
                                            {{ field_value|default:"-"|truncatechars:50 }}
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">
                                            <i class="fas fa-minus-circle"></i> غير متوفر
                                        </span>
                                    {% endif %}
                                {% endwith %}
                            </td>
                            {% endfor %}
                            <td>{{ record.created_at|date:"Y-m-d" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'tables:data_detail' table.id record.id %}" class="btn btn-info">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <a href="{% url 'tables:data_edit' table.id record.id %}" class="btn btn-primary">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <a href="{% url 'tables:data_delete' table.id record.id %}" class="btn btn-danger">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="{{ fields|length|add:3 }}" class="text-center">لا توجد بيانات متاحة.</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>

        {% if records.paginator.num_pages > 1 %}
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if records.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if sort_field %}&sort={{ sort_field }}{% endif %}{% if sort_dir %}&dir={{ sort_dir }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ records.previous_page_number }}{% if sort_field %}&sort={{ sort_field }}{% endif %}{% if sort_dir %}&dir={{ sort_dir }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-angle-double-right"></i></span>
                </li>
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-angle-right"></i></span>
                </li>
                {% endif %}

                {% for num in records.paginator.page_range %}
                    {% if records.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                    {% elif num > records.number|add:'-3' and num < records.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if sort_field %}&sort={{ sort_field }}{% endif %}{% if sort_dir %}&dir={{ sort_dir }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">
                            {{ num }}
                        </a>
                    </li>
                    {% endif %}
                {% endfor %}

                {% if records.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ records.next_page_number }}{% if sort_field %}&sort={{ sort_field }}{% endif %}{% if sort_dir %}&dir={{ sort_dir }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ records.paginator.num_pages }}{% if sort_field %}&sort={{ sort_field }}{% endif %}{% if sort_dir %}&dir={{ sort_dir }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-angle-left"></i></span>
                </li>
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-angle-double-left"></i></span>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Custom template filter for getting attribute
    function getattribute(obj, attr) {
        return obj[attr];
    }

    // طباعة معلومات تشخيصية
    console.log("Records:", {{ records|safe }});
    console.log("Fields:", {{ fields|safe }});
    console.log("Table DB Name:", "{{ table.db_table_name }}");

    {% for record in records %}
    console.log("Record {{ forloop.counter }}:", {
        "id": "{{ record.id }}",
        {% for field in fields %}
        "{{ field.name }}": "{{ record|getattribute:field.name|default:'' }}",
        {% endfor %}
        "created_at": "{{ record.created_at }}",
        "updated_at": "{{ record.updated_at }}",
        "created_by_id": "{{ record.created_by_id }}"
    });
    {% endfor %}
</script>
{% endblock %}
