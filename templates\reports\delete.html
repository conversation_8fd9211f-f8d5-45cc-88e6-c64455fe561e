{% extends 'base.html' %}
{% load table_tags %}

{% block title %}حذف تقرير - {{ report.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:list' %}">التقارير</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:detail' report.id %}">{{ report.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">حذف</li>
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">حذف التقرير: {{ report.name }}</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> تحذير: هل أنت متأكد من رغبتك في حذف هذا التقرير؟ هذا الإجراء لا يمكن التراجع عنه.
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">معلومات التقرير</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">اسم التقرير:</dt>
                    <dd class="col-sm-9">{{ report.name }}</dd>
                    
                    <dt class="col-sm-3">القسم:</dt>
                    <dd class="col-sm-9">{{ report.department.name }}</dd>
                    
                    <dt class="col-sm-3">الجدول:</dt>
                    <dd class="col-sm-9">{{ report.table.name }}</dd>
                    
                    <dt class="col-sm-3">نوع التقرير:</dt>
                    <dd class="col-sm-9">{{ report.get_report_type_display }}</dd>
                    
                    <dt class="col-sm-3">تاريخ الإنشاء:</dt>
                    <dd class="col-sm-9">{{ report.created_at|date:"Y-m-d H:i" }}</dd>
                    
                    <dt class="col-sm-3">بواسطة:</dt>
                    <dd class="col-sm-9">{{ report.created_by.get_full_name|default:report.created_by.username }}</dd>
                </dl>
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between">
                <a href="{% url 'reports:detail' report.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> تأكيد الحذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
