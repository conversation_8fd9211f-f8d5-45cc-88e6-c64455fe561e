{% extends 'base.html' %}
{% load table_tags %}

{% block title %}التقارير - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item active" aria-current="page">التقارير</li>
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">التقارير</h5>
    </div>
    <div class="card-body">
        {% if reports %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم التقرير</th>
                        <th>القسم</th>
                        <th>الجدول</th>
                        <th>نوع التقرير</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for report in reports %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ report.name }}</td>
                        <td>{{ report.department.name }}</td>
                        <td>{{ report.table.name }}</td>
                        <td>{{ report.get_report_type_display }}</td>
                        <td>{{ report.created_at|date:"Y-m-d" }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'reports:detail' report.id %}" class="btn btn-info">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                {% if request.user.is_superuser or report.created_by == request.user %}
                                <a href="{% url 'reports:edit' report.id %}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'reports:delete' report.id %}" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                                {% endif %}
                                <a href="{% url 'reports:export' report.id %}" class="btn btn-success">
                                    <i class="fas fa-file-export"></i> تصدير
                                </a>
                                <a href="{% url 'reports:print' report.id %}" class="btn btn-secondary" target="_blank">
                                    <i class="fas fa-print"></i> طباعة
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> لا توجد تقارير متاحة.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
