{% extends 'base.html' %}

{% block title %}{{ title }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        {% if table %}
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        {% endif %}
        <li class="breadcrumb-item active" aria-current="page">{{ title }}</li>
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{{ title }}</h5>
    </div>
    <div class="card-body">
        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="id_name" class="form-label">اسم الجدول</label>
                    <input type="text" name="name" id="id_name" class="form-control {% if form.name.errors %}is-invalid{% endif %}" value="{{ form.name.value|default:'' }}" required>
                    {% if form.name.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.name.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-6">
                    <label for="id_order" class="form-label">الترتيب</label>
                    <input type="number" name="order" id="id_order" class="form-control {% if form.order.errors %}is-invalid{% endif %}" value="{{ form.order.value|default:'0' }}">
                    {% if form.order.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.order.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="id_icon" class="form-label">أيقونة الجدول</label>
                    <div class="input-group">
                        <span class="input-group-text"><i id="icon-preview" class="{{ form.icon.value|default:'fas fa-table' }}"></i></span>
                        <input type="text" name="icon" id="id_icon" class="form-control {% if form.icon.errors %}is-invalid{% endif %}" value="{{ form.icon.value|default:'fas fa-table' }}">
                        <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#iconPickerModal">
                            <i class="fas fa-search"></i> اختر
                        </button>
                    </div>
                    {% if form.icon.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.icon.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-6">
                    <label for="id_color" class="form-label">لون الجدول</label>
                    <div class="input-group">
                        <input type="color" class="form-control form-control-color" id="color-picker" value="{{ form.color.value|default:'#0d6efd' }}">
                        <input type="text" name="color" id="id_color" class="form-control {% if form.color.errors %}is-invalid{% endif %}" value="{{ form.color.value|default:'#0d6efd' }}">
                    </div>
                    {% if form.color.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.color.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="mb-3">
                <label for="id_description" class="form-label">وصف الجدول</label>
                <textarea name="description" id="id_description" class="form-control {% if form.description.errors %}is-invalid{% endif %}" rows="3">{{ form.description.value|default:'' }}</textarea>
                {% if form.description.errors %}
                <div class="invalid-feedback">
                    {% for error in form.description.errors %}{{ error }}{% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="id_visibility" class="form-label">الرؤية</label>
                    <select name="visibility" id="id_visibility" class="form-control {% if form.visibility.errors %}is-invalid{% endif %}">
                        <option value="inherit" {% if form.visibility.value == 'inherit' %}selected{% endif %}>وراثة من القسم</option>
                        <option value="public" {% if form.visibility.value == 'public' %}selected{% endif %}>عام</option>
                        <option value="private" {% if form.visibility.value == 'private' %}selected{% endif %}>خاص</option>
                    </select>
                    {% if form.visibility.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.visibility.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-6">
                    <div class="form-check mt-4">
                        <input type="checkbox" name="is_active" id="id_is_active" class="form-check-input {% if form.is_active.errors %}is-invalid{% endif %}" {% if form.is_active.value %}checked{% endif %}>
                        <label for="id_is_active" class="form-check-label">نشط</label>
                        {% if form.is_active.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.is_active.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% if table %}{% url 'tables:detail' table.id %}{% else %}{% url 'departments:detail' department.id %}{% endif %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Modal for Icon Picker -->
<div class="modal fade" id="iconPickerModal" tabindex="-1" aria-labelledby="iconPickerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="iconPickerModalLabel">اختر أيقونة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="icon-search" placeholder="ابحث عن أيقونة...">
                </div>
                <div class="row" id="icon-list">
                    <!-- Icons will be loaded here via JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Color picker
    document.getElementById('color-picker').addEventListener('input', function(e) {
        document.getElementById('id_color').value = e.target.value;
    });
    
    document.getElementById('id_color').addEventListener('input', function(e) {
        document.getElementById('color-picker').value = e.target.value;
    });
    
    // Icon preview
    document.getElementById('id_icon').addEventListener('input', function(e) {
        document.getElementById('icon-preview').className = e.target.value;
    });
    
    // Icon picker
    const icons = [
        'fas fa-table', 'fas fa-list', 'fas fa-list-alt', 'fas fa-th',
        'fas fa-th-list', 'fas fa-th-large', 'fas fa-database', 'fas fa-server',
        'fas fa-file', 'fas fa-file-alt', 'fas fa-file-pdf', 'fas fa-file-word',
        'fas fa-file-excel', 'fas fa-file-image', 'fas fa-folder', 'fas fa-folder-open',
        'fas fa-clipboard', 'fas fa-clipboard-list', 'fas fa-clipboard-check',
        'fas fa-tasks', 'fas fa-check-square', 'fas fa-calendar', 'fas fa-calendar-alt',
        'fas fa-chart-bar', 'fas fa-chart-line', 'fas fa-chart-pie', 'fas fa-chart-area',
        'fas fa-users', 'fas fa-user-tie', 'fas fa-user-shield', 'fas fa-user-cog',
        'fas fa-building', 'fas fa-university', 'fas fa-landmark', 'fas fa-balance-scale',
        'fas fa-gavel', 'fas fa-book', 'fas fa-book-open', 'fas fa-bookmark',
        'fas fa-briefcase', 'fas fa-suitcase', 'fas fa-tools', 'fas fa-wrench',
        'fas fa-cog', 'fas fa-cogs', 'fas fa-project-diagram', 'fas fa-sitemap'
    ];
    
    const iconList = document.getElementById('icon-list');
    
    // Populate icon list
    icons.forEach(icon => {
        const iconDiv = document.createElement('div');
        iconDiv.className = 'col-md-2 col-sm-3 col-4 text-center mb-3 icon-item';
        iconDiv.innerHTML = `
            <div class="p-2 border rounded icon-box" data-icon="${icon}">
                <i class="${icon} fa-2x mb-2"></i>
                <small class="d-block text-truncate">${icon}</small>
            </div>
        `;
        iconList.appendChild(iconDiv);
    });
    
    // Icon search
    document.getElementById('icon-search').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        document.querySelectorAll('.icon-item').forEach(item => {
            const iconName = item.querySelector('small').textContent.toLowerCase();
            if (iconName.includes(searchTerm)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
    
    // Icon selection
    document.querySelectorAll('.icon-box').forEach(box => {
        box.addEventListener('click', function() {
            const icon = this.getAttribute('data-icon');
            document.getElementById('id_icon').value = icon;
            document.getElementById('icon-preview').className = icon;
            bootstrap.Modal.getInstance(document.getElementById('iconPickerModal')).hide();
        });
    });
</script>
{% endblock %}
