from django.db import models
from django.contrib.auth.models import User, Group
from django.urls import reverse
from django.utils.text import slugify
import uuid

class Department(models.Model):
    """
    نموذج القسم
    """
    name = models.CharField(max_length=100, verbose_name="اسم القسم")
    slug = models.SlugField(max_length=120, unique=True, blank=True, verbose_name="الاسم اللطيف")
    description = models.TextField(blank=True, verbose_name="وصف القسم")
    icon = models.CharField(max_length=50, default="fas fa-folder", verbose_name="أيقونة القسم")
    color = models.CharField(max_length=20, default="#0d6efd", verbose_name="لون القسم")
    order = models.PositiveIntegerField(default=0, verbose_name="الترتيب")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="created_departments", verbose_name="أنشئ بواسطة")

    # إعدادات الصلاحيات
    is_public = models.BooleanField(default=False, verbose_name="عام")
    allowed_users = models.ManyToManyField(User, blank=True, related_name="accessible_departments", verbose_name="المستخدمون المسموح لهم")
    allowed_groups = models.ManyToManyField(Group, blank=True, related_name="accessible_departments", verbose_name="المجموعات المسموح لها")

    class Meta:
        verbose_name = "قسم"
        verbose_name_plural = "أقسام"
        ordering = ['order', 'name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
            # إذا كان الاسم اللطيف موجودًا بالفعل، أضف معرف فريد
            if Department.objects.filter(slug=self.slug).exists():
                self.slug = f"{self.slug}-{uuid.uuid4().hex[:8]}"
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('departments:detail', args=[self.id])

    def user_has_access(self, user):
        """
        التحقق مما إذا كان المستخدم لديه حق الوصول إلى هذا القسم
        """
        if user.is_superuser or self.is_public:
            return True

        if self.created_by == user:
            return True

        if user in self.allowed_users.all():
            return True

        for group in user.groups.all():
            if group in self.allowed_groups.all():
                return True

        return False

class DepartmentAdmin(models.Model):
    """
    نموذج مدير القسم
    """
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name="admins", verbose_name="القسم")
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="administered_departments", verbose_name="المستخدم")
    is_main_admin = models.BooleanField(default=False, verbose_name="مدير رئيسي")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإضافة")

    class Meta:
        verbose_name = "مدير قسم"
        verbose_name_plural = "مدراء الأقسام"
        unique_together = ('department', 'user')

    def __str__(self):
        return f"{self.user.username} - {self.department.name}"
