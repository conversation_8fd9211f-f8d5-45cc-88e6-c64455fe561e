from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Count
from .models import Department, DepartmentAdmin
from .forms import DepartmentForm, DepartmentPermissionsForm, DepartmentAdminForm
from tables.models import Table

@login_required
def department_list(request):
    """
    عرض قائمة الأقسام
    """
    # الحصول على الأقسام المتاحة للمستخدم الحالي
    if request.user.is_superuser:
        departments = Department.objects.all()
    else:
        # الأقسام العامة أو التي أنشأها المستخدم أو المسموح له بالوصول إليها
        departments = Department.objects.filter(
            is_active=True
        ).filter(
            is_public=True
        ) | Department.objects.filter(
            created_by=request.user
        ) | Department.objects.filter(
            allowed_users=request.user
        ) | Department.objects.filter(
            allowed_groups__in=request.user.groups.all()
        ).distinct()

    # إضافة عدد الجداول لكل قسم
    departments = departments.annotate(tables_count=Count('tables'))

    context = {
        'departments': departments
    }

    return render(request, 'departments/list.html', context)

@login_required
def department_detail(request, department_id):
    """
    عرض تفاصيل القسم
    """
    department = get_object_or_404(Department, id=department_id)

    # التحقق من صلاحية الوصول
    if not department.user_has_access(request.user):
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذا القسم")
        return redirect('departments:list')

    # الحصول على جداول القسم
    tables = Table.objects.filter(department=department, is_active=True).order_by('order', 'name')

    # الحصول على مدراء القسم
    admins = DepartmentAdmin.objects.filter(department=department)

    context = {
        'department': department,
        'tables': tables,
        'admins': admins,
        'is_admin': request.user.is_superuser or department.created_by == request.user or admins.filter(user=request.user).exists()
    }

    return render(request, 'departments/detail.html', context)

@login_required
def department_create(request):
    """
    إنشاء قسم جديد
    """
    # التحقق من صلاحية الإنشاء
    if not request.user.is_staff:
        messages.error(request, "ليس لديك صلاحية لإنشاء قسم جديد")
        return redirect('departments:list')

    if request.method == 'POST':
        form = DepartmentForm(request.POST)
        if form.is_valid():
            department = form.save(commit=False)
            department.created_by = request.user
            department.save()

            # إضافة المستخدم الحالي كمدير رئيسي للقسم
            DepartmentAdmin.objects.create(
                department=department,
                user=request.user,
                is_main_admin=True
            )

            messages.success(request, f"تم إنشاء القسم '{department.name}' بنجاح")
            return redirect('departments:detail', department_id=department.id)
    else:
        form = DepartmentForm()

    context = {
        'form': form,
        'title': 'إنشاء قسم جديد'
    }

    return render(request, 'departments/form.html', context)

@login_required
def department_edit(request, department_id):
    """
    تعديل قسم
    """
    department = get_object_or_404(Department, id=department_id)

    # التحقق من صلاحية التعديل
    if not (request.user.is_superuser or department.created_by == request.user or DepartmentAdmin.objects.filter(department=department, user=request.user, is_main_admin=True).exists()):
        messages.error(request, "ليس لديك صلاحية لتعديل هذا القسم")
        return redirect('departments:detail', department_id=department.id)

    if request.method == 'POST':
        form = DepartmentForm(request.POST, instance=department)
        if form.is_valid():
            form.save()
            messages.success(request, f"تم تعديل القسم '{department.name}' بنجاح")
            return redirect('departments:detail', department_id=department.id)
    else:
        form = DepartmentForm(instance=department)

    context = {
        'form': form,
        'department': department,
        'title': f"تعديل القسم: {department.name}"
    }

    return render(request, 'departments/form.html', context)

@login_required
def department_delete(request, department_id):
    """
    حذف قسم
    """
    department = get_object_or_404(Department, id=department_id)

    # التحقق من صلاحية الحذف
    if not (request.user.is_superuser or department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لحذف هذا القسم")
        return redirect('departments:detail', department_id=department.id)

    if request.method == 'POST':
        department_name = department.name
        department_id = department.id

        # تنظيف جميع النماذج الديناميكية للقسم من ذاكرة التطبيق
        from tables.dynamic_models import DynamicModelManager
        DynamicModelManager.clean_all_models_for_department(department_id)

        # حذف القسم
        department.delete()

        messages.success(request, f"تم حذف القسم '{department_name}' بنجاح")
        return redirect('departments:list')

    context = {
        'department': department
    }

    return render(request, 'departments/delete.html', context)

@login_required
def department_permissions(request, department_id):
    """
    إدارة صلاحيات القسم
    """
    department = get_object_or_404(Department, id=department_id)

    # التحقق من صلاحية إدارة الصلاحيات
    if not (request.user.is_superuser or department.created_by == request.user or DepartmentAdmin.objects.filter(department=department, user=request.user, is_main_admin=True).exists()):
        messages.error(request, "ليس لديك صلاحية لإدارة صلاحيات هذا القسم")
        return redirect('departments:detail', department_id=department.id)

    if request.method == 'POST':
        form = DepartmentPermissionsForm(request.POST, instance=department)
        if form.is_valid():
            form.save()
            messages.success(request, f"تم تحديث صلاحيات القسم '{department.name}' بنجاح")
            return redirect('departments:detail', department_id=department.id)
    else:
        form = DepartmentPermissionsForm(instance=department)

    # الحصول على مدراء القسم
    admins = DepartmentAdmin.objects.filter(department=department)

    context = {
        'form': form,
        'department': department,
        'admins': admins,
        'title': f"إدارة صلاحيات القسم: {department.name}"
    }

    return render(request, 'departments/permissions.html', context)
