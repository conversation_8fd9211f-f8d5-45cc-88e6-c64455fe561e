{% extends 'base.html' %}

{% block title %}{% if relation %}تعديل علاقة{% else %}إنشاء علاقة جديدة{% endif %} - {{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:relation_list' table.id %}">العلاقات</a></li>
        <li class="breadcrumb-item active" aria-current="page">{% if relation %}تعديل علاقة{% else %}إنشاء علاقة جديدة{% endif %}</li>
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{% if relation %}تعديل علاقة{% else %}إنشاء علاقة جديدة{% endif %} في {{ table.name }}</h5>
    </div>
    <div class="card-body">
        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> العلاقات تسمح بربط الجداول ببعضها البعض. يمكنك إنشاء علاقة بين حقل في هذا الجدول وجدول آخر.
            </div>
            
            {% if form.errors %}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> يرجى تصحيح الأخطاء أدناه.
                {{ form.errors }}
            </div>
            {% endif %}
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="{{ form.source_field.id_for_label }}" class="form-label">{{ form.source_field.label }}</label>
                    {{ form.source_field }}
                    {% if form.source_field.errors %}
                    <div class="invalid-feedback d-block">
                        {{ form.source_field.errors }}
                    </div>
                    {% endif %}
                    <div class="form-text">{{ form.source_field.help_text }}</div>
                </div>
                
                <div class="col-md-6">
                    <label for="{{ form.target_table.id_for_label }}" class="form-label">{{ form.target_table.label }}</label>
                    {{ form.target_table }}
                    {% if form.target_table.errors %}
                    <div class="invalid-feedback d-block">
                        {{ form.target_table.errors }}
                    </div>
                    {% endif %}
                    <div class="form-text">{{ form.target_table.help_text }}</div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="{{ form.relation_type.id_for_label }}" class="form-label">{{ form.relation_type.label }}</label>
                    {{ form.relation_type }}
                    {% if form.relation_type.errors %}
                    <div class="invalid-feedback d-block">
                        {{ form.relation_type.errors }}
                    </div>
                    {% endif %}
                    <div class="form-text">{{ form.relation_type.help_text }}</div>
                </div>
                
                <div class="col-md-6">
                    <label for="{{ form.display_field.id_for_label }}" class="form-label">{{ form.display_field.label }}</label>
                    {{ form.display_field }}
                    {% if form.display_field.errors %}
                    <div class="invalid-feedback d-block">
                        {{ form.display_field.errors }}
                    </div>
                    {% endif %}
                    <div class="form-text">{{ form.display_field.help_text }}</div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                    <div class="invalid-feedback d-block">
                        {{ form.description.errors }}
                    </div>
                    {% endif %}
                    <div class="form-text">{{ form.description.help_text }}</div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% url 'tables:relation_list' table.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> {% if relation %}تحديث{% else %}حفظ{% endif %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحديث حقول العرض عند تغيير الجدول المستهدف
    document.getElementById('id_target_table').addEventListener('change', function() {
        const targetTableId = this.value;
        const displayFieldSelect = document.getElementById('id_display_field');
        
        // تفريغ قائمة حقول العرض
        displayFieldSelect.innerHTML = '';
        
        if (targetTableId) {
            // إضافة خيار "جاري التحميل..."
            const loadingOption = document.createElement('option');
            loadingOption.text = 'جاري تحميل الحقول...';
            displayFieldSelect.add(loadingOption);
            
            // طلب الحقول من الخادم
            fetch(`/tables/api/fields/${targetTableId}/`)
                .then(response => response.json())
                .then(data => {
                    // تفريغ القائمة
                    displayFieldSelect.innerHTML = '';
                    
                    // إضافة الحقول
                    data.forEach(field => {
                        const option = document.createElement('option');
                        option.value = field.id;
                        option.text = field.label;
                        displayFieldSelect.add(option);
                    });
                })
                .catch(error => {
                    console.error('Error fetching fields:', error);
                    displayFieldSelect.innerHTML = '';
                    const errorOption = document.createElement('option');
                    errorOption.text = 'خطأ في تحميل الحقول';
                    displayFieldSelect.add(errorOption);
                });
        }
    });
</script>
{% endblock %}
