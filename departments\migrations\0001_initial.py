# Generated by Django 4.2.20 on 2025-05-12 07:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القسم')),
                ('slug', models.SlugField(blank=True, max_length=120, unique=True, verbose_name='الاسم اللطيف')),
                ('description', models.TextField(blank=True, verbose_name='وصف القسم')),
                ('icon', models.CharField(default='fas fa-folder', max_length=50, verbose_name='أيقونة القسم')),
                ('color', models.CharField(default='#0d6efd', max_length=20, verbose_name='لون القسم')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('is_public', models.BooleanField(default=False, verbose_name='عام')),
                ('allowed_groups', models.ManyToManyField(blank=True, related_name='accessible_departments', to='auth.group', verbose_name='المجموعات المسموح لها')),
                ('allowed_users', models.ManyToManyField(blank=True, related_name='accessible_departments', to=settings.AUTH_USER_MODEL, verbose_name='المستخدمون المسموح لهم')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_departments', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'أقسام',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='DepartmentAdmin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_main_admin', models.BooleanField(default=False, verbose_name='مدير رئيسي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='admins', to='departments.department', verbose_name='القسم')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='administered_departments', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'مدير قسم',
                'verbose_name_plural': 'مدراء الأقسام',
                'unique_together': {('department', 'user')},
            },
        ),
    ]
