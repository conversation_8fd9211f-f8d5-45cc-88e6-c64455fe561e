{% extends 'base.html' %}
{% load table_tags %}

{% block title %}حذف قسم - {{ department.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">حذف</li>
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">حذف القسم: {{ department.name }}</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> تحذير: هل أنت متأكد من رغبتك في حذف هذا القسم؟ هذا الإجراء لا يمكن التراجع عنه.
        </div>
        
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i> <strong>تنبيه هام:</strong> حذف هذا القسم سيؤدي إلى حذف جميع الجداول والبيانات المرتبطة به.
        </div>
        
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">معلومات القسم</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">اسم القسم:</dt>
                    <dd class="col-sm-9">{{ department.name }}</dd>
                    
                    <dt class="col-sm-3">الوصف:</dt>
                    <dd class="col-sm-9">{{ department.description|default:"-" }}</dd>
                    
                    <dt class="col-sm-3">تاريخ الإنشاء:</dt>
                    <dd class="col-sm-9">{{ department.created_at|date:"Y-m-d H:i" }}</dd>
                    
                    <dt class="col-sm-3">بواسطة:</dt>
                    <dd class="col-sm-9">{{ department.created_by.get_full_name|default:department.created_by.username }}</dd>
                    
                    <dt class="col-sm-3">عدد الجداول:</dt>
                    <dd class="col-sm-9">{{ department.table_set.count }}</dd>
                </dl>
            </div>
        </div>
        
        {% if department.table_set.exists %}
        <div class="card mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">الجداول التي سيتم حذفها</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم الجدول</th>
                                <th>عدد الحقول</th>
                                <th>عدد السجلات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for table in department.table_set.all %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ table.name }}</td>
                                <td>{{ table.field_set.count }}</td>
                                <td>{{ table.record_count|default:"0" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
        
        <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between">
                <a href="{% url 'departments:detail' department.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> تأكيد الحذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
