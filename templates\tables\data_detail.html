{% extends 'base.html' %}
{% load table_tags %}

{% block title %}تفاصيل السجل - {{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:data_list' table.id %}">البيانات</a></li>
        <li class="breadcrumb-item active" aria-current="page">تفاصيل السجل</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="{{ table.icon }}" style="color: {{ table.color }}"></i>
        تفاصيل السجل
    </h1>

    <div class="btn-group">
        <a href="{% url 'tables:data_list' table.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى القائمة
        </a>
        <a href="{% url 'tables:data_edit' table.id record.id %}" class="btn btn-primary">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'tables:data_delete' table.id record.id %}" class="btn btn-danger">
            <i class="fas fa-trash"></i> حذف
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">بيانات السجل</h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for field in fields %}
            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0">{{ field.label }}</h6>
                    </div>
                    <div class="card-body">
                        {% with value=record|getattribute:field.name %}
                        {% if field.field_type == 'boolean' %}
                            {% if value %}
                            <span class="badge bg-success"><i class="fas fa-check"></i> نعم</span>
                            {% else %}
                            <span class="badge bg-secondary"><i class="fas fa-times"></i> لا</span>
                            {% endif %}
                        {% elif field.field_type == 'date' %}
                            {{ value|date:"Y-m-d" }}
                        {% elif field.field_type == 'datetime' %}
                            {{ value|date:"Y-m-d H:i" }}
                        {% elif field.field_type == 'file' or field.field_type == 'document' or field.field_type == 'pdf' %}
                            {% if value %}
                            <a href="{{ value.url }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="fas fa-file"></i> عرض الملف
                            </a>
                            {% else %}
                            <span class="text-muted">لا يوجد ملف</span>
                            {% endif %}
                        {% elif field.field_type == 'image' %}
                            {% if value %}
                            <a href="{{ value.url }}" target="_blank">
                                <img src="{{ value.url }}" alt="{{ field.label }}" class="img-fluid img-thumbnail">
                            </a>
                            {% else %}
                            <span class="text-muted">لا توجد صورة</span>
                            {% endif %}
                        {% elif field.field_type == 'textarea' or field.field_type == 'html' %}
                            <div class="text-content">
                                {{ value|linebreaks }}
                            </div>
                        {% elif field.field_type == 'url' %}
                            {% if value %}
                            <a href="{{ value }}" target="_blank">{{ value }}</a>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        {% elif field.field_type == 'email' %}
                            {% if value %}
                            <a href="mailto:{{ value }}">{{ value }}</a>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        {% else %}
                            {{ value|default:"<span class='text-muted'>-</span>" }}
                        {% endif %}
                        {% endwith %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header bg-secondary text-white">
        <h5 class="mb-0">معلومات النظام</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <dl class="row">
                    <dt class="col-sm-4">تاريخ الإنشاء:</dt>
                    <dd class="col-sm-8">{{ record.created_at|date:"Y-m-d H:i" }}</dd>

                    <dt class="col-sm-4">بواسطة:</dt>
                    <dd class="col-sm-8">{{ record.created_by.get_full_name|default:record.created_by.username }}</dd>
                </dl>
            </div>
            <div class="col-md-6">
                <dl class="row">
                    <dt class="col-sm-4">آخر تحديث:</dt>
                    <dd class="col-sm-8">{{ record.updated_at|date:"Y-m-d H:i" }}</dd>

                    <dt class="col-sm-4">معرف السجل:</dt>
                    <dd class="col-sm-8">{{ record.id }}</dd>
                </dl>
            </div>
        </div>
    </div>
</div>

{% if related_records %}
<div class="card mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">السجلات المرتبطة</h5>
    </div>
    <div class="card-body">
        <ul class="nav nav-tabs" id="relatedTabs" role="tablist">
            {% for relation_name, relation_data in related_records.items %}
            <li class="nav-item" role="presentation">
                <button class="nav-link {% if forloop.first %}active{% endif %}" id="{{ relation_name }}-tab" data-bs-toggle="tab" data-bs-target="#{{ relation_name }}" type="button" role="tab" aria-controls="{{ relation_name }}" aria-selected="{% if forloop.first %}true{% else %}false{% endif %}">
                    {{ relation_data.label }} ({{ relation_data.records|length }})
                </button>
            </li>
            {% endfor %}
        </ul>
        <div class="tab-content p-3 border border-top-0 rounded-bottom" id="relatedTabsContent">
            {% for relation_name, relation_data in related_records.items %}
            <div class="tab-pane fade {% if forloop.first %}show active{% endif %}" id="{{ relation_name }}" role="tabpanel" aria-labelledby="{{ relation_name }}-tab">
                {% if relation_data.records %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                {% for field in relation_data.fields|slice:":5" %}
                                <th>{{ field.label }}</th>
                                {% endfor %}
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for related_record in relation_data.records %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                {% for field in relation_data.fields|slice:":5" %}
                                <td>{{ related_record|getattribute:field.name|default:"-" }}</td>
                                {% endfor %}
                                <td>
                                    <a href="{% url 'tables:data_detail' relation_data.table.id related_record.id %}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> لا توجد سجلات مرتبطة.
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
