from django.urls import path
from . import views

urlpatterns = [
    path('', views.table_list, name='list'),
    path('create/<int:department_id>/', views.table_create, name='create'),
    path('<int:table_id>/', views.table_detail, name='detail'),
    path('<int:table_id>/edit/', views.table_edit, name='edit'),
    path('<int:table_id>/delete/', views.table_delete, name='delete'),
    path('<int:table_id>/fields/', views.field_list, name='field_list'),
    path('<int:table_id>/fields/create/', views.field_create, name='field_create'),
    path('<int:table_id>/fields/<int:field_id>/edit/', views.field_edit, name='field_edit'),
    path('<int:table_id>/fields/<int:field_id>/delete/', views.field_delete, name='field_delete'),
    path('<int:table_id>/data/', views.data_list, name='data_list'),
    path('<int:table_id>/data/create/', views.data_create, name='data_create'),
    path('<int:table_id>/data/<int:record_id>/', views.data_detail, name='data_detail'),
    path('<int:table_id>/data/<int:record_id>/edit/', views.data_edit, name='data_edit'),
    path('<int:table_id>/data/<int:record_id>/delete/', views.data_delete, name='data_delete'),
    path('<int:table_id>/import/', views.data_import, name='data_import'),
    path('<int:table_id>/export/', views.data_export, name='data_export'),
    path('<int:table_id>/relations/', views.relation_list, name='relation_list'),
    path('<int:table_id>/relations/create/', views.relation_create, name='relation_create'),
    path('<int:table_id>/relations/<int:relation_id>/edit/', views.relation_edit, name='relation_edit'),
    path('<int:table_id>/relations/<int:relation_id>/delete/', views.relation_delete, name='relation_delete'),
    path('<int:table_id>/fix/', views.fix_table, name='fix_table'),
]
