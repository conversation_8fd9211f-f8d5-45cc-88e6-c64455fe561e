from django import forms
from .models import Table, Field, TableRelation

class TableForm(forms.ModelForm):
    """
    نموذج إنشاء وتعديل الجدول
    """
    class Meta:
        model = Table
        fields = ['name', 'description', 'icon', 'color', 'order', 'visibility', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل اسم الجدول'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'أدخل وصف الجدول'}),
            'icon': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مثال: fas fa-table'}),
            'color': forms.TextInput(attrs={'class': 'form-control color-picker', 'placeholder': 'مثال: #0d6efd'}),
            'order': forms.NumberInput(attrs={'class': 'form-control'}),
            'visibility': forms.Select(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class FieldForm(forms.ModelForm):
    """
    نموذج إنشاء وتعديل الحقل
    """
    class Meta:
        model = Field
        fields = [
            'name', 'label', 'field_type', 'description', 'is_required', 'is_unique',
            'is_indexed', 'is_searchable', 'default_value', 'placeholder', 'help_text',
            'order', 'options', 'validation_regex', 'min_value', 'max_value', 'is_active'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل اسم الحقل (بالإنجليزية بدون مسافات)'}),
            'label': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل عنوان الحقل'}),
            'field_type': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'أدخل وصف الحقل'}),
            'is_required': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_unique': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_indexed': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_searchable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'default_value': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'القيمة الافتراضية'}),
            'placeholder': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'النص التوضيحي'}),
            'help_text': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'نص المساعدة'}),
            'order': forms.NumberInput(attrs={'class': 'form-control'}),
            'options': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'خيارات الحقل (JSON أو قائمة مفصولة بفواصل)'}),
            'validation_regex': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'تعبير منتظم للتحقق'}),
            'min_value': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'القيمة الدنيا'}),
            'max_value': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'القيمة القصوى'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def clean_name(self):
        name = self.cleaned_data.get('name')
        if name:
            # التأكد من أن الاسم يحتوي على أحرف إنجليزية وأرقام وشرطة سفلية فقط
            import re
            if not re.match(r'^[a-zA-Z0-9_]+$', name):
                raise forms.ValidationError("يجب أن يحتوي اسم الحقل على أحرف إنجليزية وأرقام وشرطة سفلية فقط")
        return name

class TableRelationForm(forms.ModelForm):
    """
    نموذج إنشاء وتعديل العلاقة بين الجداول
    """
    class Meta:
        model = TableRelation
        fields = ['name', 'target_table', 'source_field', 'relation_type', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل اسم العلاقة'}),
            'target_table': forms.Select(attrs={'class': 'form-control'}),
            'source_field': forms.Select(attrs={'class': 'form-control'}),
            'relation_type': forms.Select(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 2, 'placeholder': 'أدخل وصف العلاقة'}),
        }

class DataImportForm(forms.Form):
    """
    نموذج استيراد البيانات
    """
    file = forms.FileField(
        label="ملف البيانات",
        widget=forms.FileInput(attrs={'class': 'form-control'})
    )
    
    file_type = forms.ChoiceField(
        label="نوع الملف",
        choices=[
            ('csv', 'CSV'),
            ('excel', 'Excel'),
            ('json', 'JSON'),
        ],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    has_header = forms.BooleanField(
        label="يحتوي على صف عناوين",
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    encoding = forms.ChoiceField(
        label="ترميز الملف",
        choices=[
            ('utf-8', 'UTF-8'),
            ('utf-8-sig', 'UTF-8 with BOM'),
            ('windows-1256', 'Windows Arabic (1256)'),
            ('iso-8859-6', 'ISO Arabic (8859-6)'),
        ],
        initial='utf-8',
        widget=forms.Select(attrs={'class': 'form-control'})
    )

class DataExportForm(forms.Form):
    """
    نموذج تصدير البيانات
    """
    file_type = forms.ChoiceField(
        label="نوع الملف",
        choices=[
            ('csv', 'CSV'),
            ('excel', 'Excel'),
            ('pdf', 'PDF'),
            ('json', 'JSON'),
        ],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    include_header = forms.BooleanField(
        label="تضمين صف العناوين",
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    encoding = forms.ChoiceField(
        label="ترميز الملف",
        choices=[
            ('utf-8', 'UTF-8'),
            ('utf-8-sig', 'UTF-8 with BOM'),
            ('windows-1256', 'Windows Arabic (1256)'),
            ('iso-8859-6', 'ISO Arabic (8859-6)'),
        ],
        initial='utf-8',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
