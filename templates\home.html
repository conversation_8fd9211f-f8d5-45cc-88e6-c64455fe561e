{% extends 'base.html' %}

{% block title %}الصفحة الرئيسية - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<div class="welcome-section text-center">
    <h1 class="display-4">مرحباً بك في نظام إدارة الدائرة القانونية</h1>
    <p class="lead">هيئة المنافذ الحدودية</p>
    
    {% if not user.is_authenticated %}
    <div class="mt-4">
        <a href="{% url 'login' %}" class="btn btn-primary btn-lg">
            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
        </a>
    </div>
    {% endif %}
</div>

{% if user.is_authenticated %}
<div class="row mt-5">
    <div class="col-md-12">
        <h2 class="mb-4">الأقسام المتاحة</h2>
    </div>
    
    {% if departments %}
    {% for department in departments %}
    <div class="col-md-4 mb-4">
        <div class="card department-card h-100">
            <div class="card-body text-center">
                <div class="department-icon text-primary">
                    <i class="{{ department.icon|default:'fas fa-folder' }}"></i>
                </div>
                <h3 class="card-title">{{ department.name }}</h3>
                <p class="card-text">{{ department.description|truncatewords:20 }}</p>
                <a href="{% url 'departments:detail' department.id %}" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left"></i> الدخول إلى القسم
                </a>
            </div>
        </div>
    </div>
    {% endfor %}
    {% else %}
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> لا توجد أقسام متاحة حالياً.
        </div>
    </div>
    {% endif %}
    
    {% if user.is_staff %}
    <div class="col-md-4 mb-4">
        <div class="card department-card h-100 border-dashed">
            <div class="card-body text-center">
                <div class="department-icon text-muted">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <h3 class="card-title">إنشاء قسم جديد</h3>
                <p class="card-text">قم بإنشاء قسم جديد وتخصيص الجداول والحقول حسب احتياجاتك.</p>
                <a href="{% url 'departments:create' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-plus"></i> إنشاء قسم
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<div class="row mt-5">
    <div class="col-md-12">
        <h2 class="mb-4">لوحة المعلومات</h2>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-chart-line"></i> النشاط الأخير
            </div>
            <div class="card-body">
                <p class="card-text text-muted">سيتم عرض النشاط الأخير هنا.</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-tasks"></i> المهام العاجلة
            </div>
            <div class="card-body">
                <p class="card-text text-muted">سيتم عرض المهام العاجلة هنا.</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
