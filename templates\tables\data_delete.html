{% extends 'base.html' %}
{% load table_tags %}

{% block title %}حذف سجل - {{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:data_list' table.id %}">البيانات</a></li>
        <li class="breadcrumb-item active" aria-current="page">حذف سجل</li>
    </ol>
</nav>

<div class="card border-danger">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">تأكيد الحذف</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> تحذير: سيؤدي حذف هذا السجل إلى حذف جميع البيانات المرتبطة به بشكل دائم. هذا الإجراء لا يمكن التراجع عنه.
        </div>

        <p>هل أنت متأكد من رغبتك في حذف هذا السجل من جدول <strong>{{ table.name }}</strong>؟</p>

        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">معلومات السجل</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for field in fields|slice:":6" %}
                    <div class="col-md-6 mb-2">
                        <dl class="row mb-0">
                            <dt class="col-sm-4">{{ field.label }}:</dt>
                            <dd class="col-sm-8">
                                {% with value=record|getattribute:field.name %}
                                {% if field.field_type == 'boolean' %}
                                    {% if value %}
                                    <span class="badge bg-success"><i class="fas fa-check"></i> نعم</span>
                                    {% else %}
                                    <span class="badge bg-secondary"><i class="fas fa-times"></i> لا</span>
                                    {% endif %}
                                {% elif field.field_type == 'date' %}
                                    {{ value|date:"Y-m-d" }}
                                {% elif field.field_type == 'datetime' %}
                                    {{ value|date:"Y-m-d H:i" }}
                                {% elif field.field_type == 'file' or field.field_type == 'image' or field.field_type == 'pdf' or field.field_type == 'document' %}
                                    {% if value %}
                                    <span class="badge bg-info"><i class="fas fa-file"></i> ملف مرفق</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                {% else %}
                                    {{ value|default:"<span class='text-muted'>-</span>" }}
                                {% endif %}
                                {% endwith %}
                            </dd>
                        </dl>
                    </div>
                    {% endfor %}
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <dl class="row mb-0">
                            <dt class="col-sm-4">تاريخ الإنشاء:</dt>
                            <dd class="col-sm-8">{{ record.created_at|date:"Y-m-d H:i" }}</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row mb-0">
                            <dt class="col-sm-4">بواسطة:</dt>
                            <dd class="col-sm-8">{{ record.created_by.get_full_name|default:record.created_by.username }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between">
                <a href="{% url 'tables:data_detail' table.id record.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> تأكيد الحذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
