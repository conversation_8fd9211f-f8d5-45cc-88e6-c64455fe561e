{% extends 'base.html' %}

{% block title %}حذف جدول {{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">حذف الجدول</li>
    </ol>
</nav>

<div class="card border-danger">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">تأكيد الحذف</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> تحذير: سيؤدي حذف هذا الجدول إلى حذف جميع البيانات والحقول والعلاقات المرتبطة به بشكل دائم. هذا الإجراء لا يمكن التراجع عنه.
        </div>
        
        <p>هل أنت متأكد من رغبتك في حذف الجدول <strong>{{ table.name }}</strong> من قسم <strong>{{ department.name }}</strong>؟</p>
        
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">معلومات الجدول</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">اسم الجدول:</dt>
                    <dd class="col-sm-9">{{ table.name }}</dd>
                    
                    <dt class="col-sm-3">القسم:</dt>
                    <dd class="col-sm-9">{{ department.name }}</dd>
                    
                    <dt class="col-sm-3">الوصف:</dt>
                    <dd class="col-sm-9">{{ table.description|default:"لا يوجد وصف" }}</dd>
                    
                    <dt class="col-sm-3">عدد الحقول:</dt>
                    <dd class="col-sm-9">{{ fields_count }}</dd>
                    
                    <dt class="col-sm-3">عدد السجلات:</dt>
                    <dd class="col-sm-9">{{ records_count }}</dd>
                    
                    <dt class="col-sm-3">تاريخ الإنشاء:</dt>
                    <dd class="col-sm-9">{{ table.created_at|date:"Y-m-d H:i" }}</dd>
                </dl>
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="mb-3">
                <label for="id_confirmation" class="form-label">اكتب "{{ table.name }}" لتأكيد الحذف</label>
                <input type="text" id="id_confirmation" name="confirmation" class="form-control" required>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% url 'tables:detail' table.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-danger" id="delete-btn" disabled>
                    <i class="fas fa-trash"></i> تأكيد الحذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تفعيل زر الحذف فقط عند كتابة اسم الجدول بشكل صحيح
    document.getElementById('id_confirmation').addEventListener('input', function() {
        const tableName = "{{ table.name }}";
        const confirmation = this.value;
        const deleteBtn = document.getElementById('delete-btn');
        
        if (confirmation === tableName) {
            deleteBtn.disabled = false;
        } else {
            deleteBtn.disabled = true;
        }
    });
</script>
{% endblock %}
