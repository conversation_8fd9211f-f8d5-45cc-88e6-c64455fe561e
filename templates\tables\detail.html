{% extends 'base.html' %}

{% block title %}{{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">{{ table.name }}</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="{{ table.icon }}" style="color: {{ table.color }}"></i>
        {{ table.name }}
    </h1>

    <div class="btn-group">
        <a href="{% url 'tables:data_list' table.id %}" class="btn btn-success">
            <i class="fas fa-table"></i> عرض البيانات
        </a>
        {% if is_admin %}
        <a href="{% url 'tables:edit' table.id %}" class="btn btn-primary">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'tables:delete' table.id %}" class="btn btn-danger">
            <i class="fas fa-trash"></i> حذف
        </a>
        {% endif %}
    </div>
</div>

{% if table.description %}
<div class="card mb-4">
    <div class="card-body">
        <h5 class="card-title">وصف الجدول</h5>
        <p class="card-text">{{ table.description }}</p>
    </div>
</div>
{% endif %}

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">الحقول</h5>
                {% if is_admin %}
                <a href="{% url 'tables:field_create' table.id %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> إضافة حقل جديد
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if fields %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم الحقل</th>
                                <th>العنوان</th>
                                <th>النوع</th>
                                <th>مطلوب</th>
                                <th>فريد</th>
                                <th>الترتيب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for field in fields %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ field.name }}</td>
                                <td>{{ field.label }}</td>
                                <td>{{ field.get_field_type_display }}</td>
                                <td>
                                    {% if field.is_required %}
                                    <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if field.is_unique %}
                                    <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                    {% else %}
                                    <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                                    {% endif %}
                                </td>
                                <td>{{ field.order }}</td>
                                <td>
                                    {% if is_admin %}
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'tables:field_edit' table.id field.id %}" class="btn btn-primary">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <a href="{% url 'tables:field_delete' table.id field.id %}" class="btn btn-danger">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                    </div>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> لا توجد حقول في هذا الجدول.
                    {% if is_admin %}
                    <a href="{% url 'tables:field_create' table.id %}" class="alert-link">إضافة حقل جديد</a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">العلاقات</h5>
                {% if is_admin %}
                <a href="{% url 'tables:relation_create' table.id %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> إضافة علاقة جديدة
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if relations %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم العلاقة</th>
                                <th>الجدول الهدف</th>
                                <th>الحقل المصدر</th>
                                <th>نوع العلاقة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for relation in relations %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ relation.name }}</td>
                                <td>{{ relation.target_table.name }}</td>
                                <td>{{ relation.source_field.label }}</td>
                                <td>{{ relation.get_relation_type_display }}</td>
                                <td>
                                    {% if is_admin %}
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'tables:relation_edit' table.id relation.id %}" class="btn btn-primary">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <a href="{% url 'tables:relation_delete' table.id relation.id %}" class="btn btn-danger">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                    </div>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> لا توجد علاقات لهذا الجدول.
                    {% if is_admin %}
                    <a href="{% url 'tables:relation_create' table.id %}" class="alert-link">إضافة علاقة جديدة</a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
