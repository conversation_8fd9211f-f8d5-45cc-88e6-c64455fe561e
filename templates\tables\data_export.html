{% extends 'base.html' %}

{% block title %}تصدير بيانات - {{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:data_list' table.id %}">البيانات</a></li>
        <li class="breadcrumb-item active" aria-current="page">تصدير بيانات</li>
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">تصدير بيانات من {{ table.name }}</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> يمكنك تصدير البيانات إلى ملفات CSV أو Excel أو PDF أو JSON.
        </div>
        
        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="id_file_type" class="form-label">نوع الملف</label>
                    <select name="file_type" id="id_file_type" class="form-select {% if form.file_type.errors %}is-invalid{% endif %}" required>
                        <option value="csv" {% if form.file_type.value == 'csv' %}selected{% endif %}>CSV</option>
                        <option value="excel" {% if form.file_type.value == 'excel' %}selected{% endif %}>Excel</option>
                        <option value="pdf" {% if form.file_type.value == 'pdf' %}selected{% endif %}>PDF</option>
                        <option value="json" {% if form.file_type.value == 'json' %}selected{% endif %}>JSON</option>
                    </select>
                    {% if form.file_type.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.file_type.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-6">
                    <label for="id_encoding" class="form-label">ترميز الملف</label>
                    <select name="encoding" id="id_encoding" class="form-select {% if form.encoding.errors %}is-invalid{% endif %}">
                        <option value="utf-8" {% if form.encoding.value == 'utf-8' %}selected{% endif %}>UTF-8</option>
                        <option value="utf-16" {% if form.encoding.value == 'utf-16' %}selected{% endif %}>UTF-16</option>
                        <option value="windows-1256" {% if form.encoding.value == 'windows-1256' %}selected{% endif %}>Windows-1256 (Arabic)</option>
                        <option value="iso-8859-6" {% if form.encoding.value == 'iso-8859-6' %}selected{% endif %}>ISO-8859-6 (Arabic)</option>
                    </select>
                    {% if form.encoding.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.encoding.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="form-check">
                        <input type="checkbox" name="include_header" id="id_include_header" class="form-check-input {% if form.include_header.errors %}is-invalid{% endif %}" {% if form.include_header.value %}checked{% endif %}>
                        <label for="id_include_header" class="form-check-label">تضمين صف العناوين</label>
                        {% if form.include_header.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.include_header.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">الحقول المراد تصديرها</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for field in fields %}
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input type="checkbox" name="export_fields" id="id_export_field_{{ field.id }}" value="{{ field.id }}" class="form-check-input" checked>
                                <label for="id_export_field_{{ field.id }}" class="form-check-label">{{ field.label }} ({{ field.name }})</label>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% url 'tables:data_list' table.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-file-export"></i> تصدير البيانات
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">معلومات الجدول</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <dl class="row">
                    <dt class="col-sm-4">اسم الجدول:</dt>
                    <dd class="col-sm-8">{{ table.name }}</dd>
                    
                    <dt class="col-sm-4">القسم:</dt>
                    <dd class="col-sm-8">{{ department.name }}</dd>
                    
                    <dt class="col-sm-4">الوصف:</dt>
                    <dd class="col-sm-8">{{ table.description|default:"لا يوجد وصف" }}</dd>
                </dl>
            </div>
            <div class="col-md-6">
                <dl class="row">
                    <dt class="col-sm-4">عدد الحقول:</dt>
                    <dd class="col-sm-8">{{ fields|length }}</dd>
                    
                    <dt class="col-sm-4">عدد السجلات:</dt>
                    <dd class="col-sm-8">{{ records_count }}</dd>
                    
                    <dt class="col-sm-4">آخر تحديث:</dt>
                    <dd class="col-sm-8">{{ table.updated_at|date:"Y-m-d H:i" }}</dd>
                </dl>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تفعيل/تعطيل بعض الخيارات بناءً على نوع الملف المختار
    document.getElementById('id_file_type').addEventListener('change', function() {
        const fileType = this.value;
        const encodingField = document.getElementById('id_encoding').closest('.row');
        
        // إخفاء/إظهار حقل الترميز بناءً على نوع الملف
        if (fileType === 'pdf') {
            encodingField.style.display = 'none';
        } else {
            encodingField.style.display = 'flex';
        }
    });
    
    // تشغيل الحدث عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('id_file_type').dispatchEvent(new Event('change'));
    });
</script>
{% endblock %}
