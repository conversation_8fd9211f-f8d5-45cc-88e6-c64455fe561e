{% extends 'base.html' %}
{% load table_tags %}

{% block title %}{{ title }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:list' %}">التقارير</a></li>
        {% if report %}
        <li class="breadcrumb-item"><a href="{% url 'reports:detail' report.id %}">{{ report.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">تعديل</li>
        {% else %}
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">إنشاء تقرير جديد</li>
        {% endif %}
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{{ title }}</h5>
    </div>
    <div class="card-body">
        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                {{ error }}
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                    {{ form.name }}
                    {% if form.name.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.name.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                    {% if form.name.help_text %}
                    <small class="form-text text-muted">{{ form.name.help_text }}</small>
                    {% endif %}
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="{{ form.table.id_for_label }}" class="form-label">{{ form.table.label }}</label>
                    {{ form.table }}
                    {% if form.table.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.table.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                    {% if form.table.help_text %}
                    <small class="form-text text-muted">{{ form.table.help_text }}</small>
                    {% endif %}
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.report_type.id_for_label }}" class="form-label">{{ form.report_type.label }}</label>
                    {{ form.report_type }}
                    {% if form.report_type.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.report_type.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                    {% if form.report_type.help_text %}
                    <small class="form-text text-muted">{{ form.report_type.help_text }}</small>
                    {% endif %}
                </div>
                
                <div class="col-md-6 mb-3 chart-options" style="display: none;">
                    <label for="{{ form.chart_type.id_for_label }}" class="form-label">{{ form.chart_type.label }}</label>
                    {{ form.chart_type }}
                    {% if form.chart_type.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.chart_type.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                    {% if form.chart_type.help_text %}
                    <small class="form-text text-muted">{{ form.chart_type.help_text }}</small>
                    {% endif %}
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="form-check">
                        {{ form.is_public }}
                        <label for="{{ form.is_public.id_for_label }}" class="form-check-label">{{ form.is_public.label }}</label>
                        {% if form.is_public.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.is_public.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                        {% if form.is_public.help_text %}
                        <small class="form-text text-muted">{{ form.is_public.help_text }}</small>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="form-check">
                        {{ form.is_active }}
                        <label for="{{ form.is_active.id_for_label }}" class="form-check-label">{{ form.is_active.label }}</label>
                        {% if form.is_active.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.is_active.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                        {% if form.is_active.help_text %}
                        <small class="form-text text-muted">{{ form.is_active.help_text }}</small>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                {{ form.description }}
                {% if form.description.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.description.errors %}{{ error }}{% endfor %}
                </div>
                {% endif %}
                {% if form.description.help_text %}
                <small class="form-text text-muted">{{ form.description.help_text }}</small>
                {% endif %}
            </div>
            
            <div class="d-flex justify-content-between">
                {% if report %}
                <a href="{% url 'reports:detail' report.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                {% else %}
                <a href="{% url 'departments:detail' department.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                {% endif %}
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تطبيق الأنماط على حقول النموذج
        document.querySelectorAll('input, select, textarea').forEach(function(element) {
            if (element.type !== 'checkbox' && element.type !== 'radio') {
                element.classList.add('form-control');
                
                if (element.required) {
                    element.classList.add('is-required');
                }
                
                if (element.classList.contains('is-invalid')) {
                    element.classList.add('is-invalid');
                }
            } else {
                element.classList.add('form-check-input');
            }
            
            if (element.tagName === 'SELECT') {
                element.classList.add('form-select');
            }
        });
        
        // إظهار/إخفاء خيارات الرسم البياني حسب نوع التقرير
        const reportTypeSelect = document.getElementById('{{ form.report_type.id_for_label }}');
        const chartOptions = document.querySelector('.chart-options');
        
        function toggleChartOptions() {
            if (reportTypeSelect.value === 'chart') {
                chartOptions.style.display = 'block';
            } else {
                chartOptions.style.display = 'none';
            }
        }
        
        toggleChartOptions();
        reportTypeSelect.addEventListener('change', toggleChartOptions);
    });
</script>
{% endblock %}
