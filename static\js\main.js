/**
 * نظام إدارة الدائرة القانونية - هيئة المنافذ الحدودية
 * الوظائف الرئيسية للجافاسكريبت
 */

// عند تحميل المستند
document.addEventListener('DOMContentLoaded', function() {
    // تفعيل tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // تفعيل popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // إخفاء رسائل التنبيه تلقائيًا بعد 5 ثوانٍ
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // تفعيل تحقق النموذج
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function (form) {
        form.addEventListener('submit', function (event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
});

/**
 * تنسيق التاريخ بالعربية
 * @param {Date} date - كائن التاريخ
 * @returns {string} - التاريخ المنسق بالعربية
 */
function formatDateArabic(date) {
    if (!date) return '';
    
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
    };
    
    return date.toLocaleDateString('ar-IQ', options);
}

/**
 * تنسيق الرقم بالعربية
 * @param {number} number - الرقم
 * @param {number} decimals - عدد الأرقام العشرية
 * @returns {string} - الرقم المنسق بالعربية
 */
function formatNumberArabic(number, decimals = 2) {
    if (number === null || number === undefined) return '';
    
    return number.toLocaleString('ar-IQ', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}

/**
 * إظهار رسالة تأكيد قبل الحذف
 * @param {string} message - رسالة التأكيد
 * @param {function} callback - الدالة التي سيتم استدعاؤها عند التأكيد
 */
function confirmDelete(message, callback) {
    if (confirm(message || 'هل أنت متأكد من رغبتك في الحذف؟')) {
        callback();
    }
}

/**
 * تحميل البيانات بتنسيق AJAX
 * @param {string} url - عنوان URL للطلب
 * @param {object} data - البيانات المرسلة
 * @param {function} successCallback - دالة النجاح
 * @param {function} errorCallback - دالة الخطأ
 */
function ajaxRequest(url, data, successCallback, errorCallback) {
    $.ajax({
        url: url,
        type: 'POST',
        data: data,
        dataType: 'json',
        headers: {
            'X-CSRFToken': getCsrfToken()
        },
        success: function(response) {
            if (typeof successCallback === 'function') {
                successCallback(response);
            }
        },
        error: function(xhr, status, error) {
            if (typeof errorCallback === 'function') {
                errorCallback(xhr, status, error);
            } else {
                console.error('خطأ في الطلب:', error);
            }
        }
    });
}

/**
 * الحصول على رمز CSRF من ملفات تعريف الارتباط
 * @returns {string} - رمز CSRF
 */
function getCsrfToken() {
    var cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        var cookies = document.cookie.split(';');
        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i].trim();
            if (cookie.substring(0, 'csrftoken='.length) === 'csrftoken=') {
                cookieValue = decodeURIComponent(cookie.substring('csrftoken='.length));
                break;
            }
        }
    }
    return cookieValue;
}
