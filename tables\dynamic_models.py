from django.db import models, connection
from django.apps import apps
from django.contrib.auth.models import User
import json
import datetime
import sys

class DynamicModelManager:
    """
    مدير النماذج الديناميكية
    """
    # قاموس لتخزين النماذج الديناميكية المنشأة
    _models_cache = {}

    @staticmethod
    def get_field_class(field_type):
        """
        الحصول على صنف الحقل المناسب لنوع الحقل
        """
        field_classes = {
            # نصوص
            'text': models.CharField,
            'textarea': models.TextField,
            'html': models.TextField,
            'email': models.EmailField,
            'phone': models.CharField,
            'url': models.URLField,

            # أرقام
            'integer': models.IntegerField,
            'decimal': models.DecimalField,
            'currency': models.DecimalField,
            'percentage': models.DecimalField,

            # تواريخ وأوقات
            'date': models.DateField,
            'time': models.TimeField,
            'datetime': models.DateTimeField,

            # اختيارات
            'select': models.Char<PERSON>ield,
            'multiselect': models.TextField,
            'radio': models.CharField,
            'checkbox': models.BooleanField,

            # ملفات
            'file': models.FileField,
            'image': models.ImageField,
            'pdf': models.FileField,
            'document': models.FileField,

            # أخرى
            'boolean': models.BooleanField,
            'user': models.ForeignKey,
            'relation': models.ForeignKey,
        }

        return field_classes.get(field_type, models.CharField)

    @staticmethod
    def get_field_kwargs(field):
        """
        الحصول على الوسائط المناسبة للحقل
        """
        kwargs = {
            'verbose_name': field.label,
            'null': not field.is_required,
            'blank': not field.is_required,
            'db_index': field.is_indexed,
        }

        # إضافة وسائط إضافية حسب نوع الحقل
        if field.field_type in ['text', 'email', 'phone', 'url', 'select', 'radio']:
            kwargs['max_length'] = 255

        if field.field_type in ['decimal', 'currency', 'percentage']:
            kwargs['max_digits'] = 15
            kwargs['decimal_places'] = 2

        if field.field_type in ['select', 'radio'] and field.options:
            try:
                options = json.loads(field.options)
                choices = [(option['value'], option['label']) for option in options]
                kwargs['choices'] = choices
            except:
                # إذا لم يكن JSON صالحًا، افترض أنه قائمة مفصولة بفواصل
                choices = [(option.strip(), option.strip()) for option in field.options.split(',') if option.strip()]
                kwargs['choices'] = choices

        if field.field_type in ['file', 'image', 'pdf', 'document']:
            kwargs['upload_to'] = f'table_files/{field.table.id}/'

        if field.field_type == 'user':
            kwargs['to'] = User
            kwargs['on_delete'] = models.SET_NULL

        if field.field_type == 'relation':
            # سيتم تعيين 'to' لاحقًا عند معرفة الجدول المرتبط
            kwargs['on_delete'] = models.SET_NULL

        if field.default_value:
            if field.field_type == 'boolean':
                kwargs['default'] = field.default_value.lower() in ['true', '1', 'yes', 'نعم']
            elif field.field_type in ['integer', 'decimal', 'currency', 'percentage']:
                try:
                    if field.field_type == 'integer':
                        kwargs['default'] = int(field.default_value)
                    else:
                        kwargs['default'] = float(field.default_value)
                except:
                    pass
            elif field.field_type in ['date', 'time', 'datetime']:
                # سيتم التعامل مع التواريخ بشكل خاص في الكود
                pass
            else:
                kwargs['default'] = field.default_value

        return kwargs

    @staticmethod
    def create_table_model(table):
        """
        إنشاء نموذج ديناميكي للجدول
        """
        from tables.models import Field, TableRelation

        # الحصول على الحقول
        fields = Field.objects.filter(table=table, is_active=True).order_by('order')

        # إنشاء قاموس الحقول
        attrs = {
            '__module__': 'tables.dynamic_models',
            'Meta': type('Meta', (), {
                'db_table': table.db_table_name,
                'verbose_name': table.name,
                'verbose_name_plural': table.name,
            }),
            'id': models.AutoField(primary_key=True),
            'created_at': models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء"),
            'updated_at': models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
            'created_by': models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name=f"+", verbose_name="أنشئ بواسطة"),
        }

        # إضافة الحقول
        for field in fields:
            field_class = DynamicModelManager.get_field_class(field.field_type)
            field_kwargs = DynamicModelManager.get_field_kwargs(field)

            # التعامل مع حقول العلاقات
            if field.field_type == 'relation':
                # الحصول على العلاقة
                try:
                    relation = TableRelation.objects.get(source_table=table, source_field=field)
                    target_model = DynamicModelManager.get_model_for_table(relation.target_table)
                    field_kwargs['to'] = target_model
                except TableRelation.DoesNotExist:
                    # إذا لم يتم العثور على العلاقة، استخدم حقل نصي بدلاً من ذلك
                    field_class = models.CharField
                    field_kwargs['max_length'] = 255

            # إضافة الحقل إلى النموذج
            attrs[field.name] = field_class(**field_kwargs)

        # إنشاء النموذج
        model = type(f"DynamicModel_{table.id}", (models.Model,), attrs)

        return model

    @staticmethod
    def get_model_for_table(table):
        """
        الحصول على النموذج الديناميكي للجدول
        """
        model_name = f"DynamicModel_{table.id}"
        app_label = 'tables'
        cache_key = f"{app_label}.{model_name}"

        # التحقق من وجود النموذج في ذاكرة التخزين المؤقت
        if cache_key in DynamicModelManager._models_cache:
            # التحقق من أن النموذج المخزن يتطابق مع الجدول الحالي
            cached_model = DynamicModelManager._models_cache[cache_key]
            if hasattr(cached_model, '_meta') and hasattr(cached_model._meta, 'db_table') and cached_model._meta.db_table == table.db_table_name:
                return cached_model

        try:
            # محاولة الحصول على النموذج إذا كان موجودًا بالفعل
            model = apps.get_model(app_label, model_name)

            # التحقق من أن النموذج المسترجع يتطابق مع الجدول الحالي
            if hasattr(model, '_meta') and hasattr(model._meta, 'db_table') and model._meta.db_table == table.db_table_name:
                # تخزين النموذج في ذاكرة التخزين المؤقت
                DynamicModelManager._models_cache[cache_key] = model
                return model
            else:
                # إذا كان النموذج المسترجع لا يتطابق مع الجدول الحالي، قم بإنشاء نموذج جديد
                model = DynamicModelManager.create_table_model(table)
                DynamicModelManager._models_cache[cache_key] = model
                return model
        except LookupError:
            # إنشاء النموذج إذا لم يكن موجودًا
            model = DynamicModelManager.create_table_model(table)
            DynamicModelManager._models_cache[cache_key] = model
            return model

    @staticmethod
    def create_table_in_database(table):
        """
        إنشاء جدول في قاعدة البيانات
        """
        import sys
        print(f"إنشاء الجدول {table.db_table_name} في قاعدة البيانات", file=sys.stderr)

        # تنظيف النموذج من ذاكرة التطبيق إذا كان موجودًا
        DynamicModelManager.clean_model_from_cache(table)

        # الحصول على النموذج الديناميكي للجدول
        model = DynamicModelManager.get_model_for_table(table)
        print(f"تم إنشاء النموذج الديناميكي للجدول {table.db_table_name}", file=sys.stderr)
        print(f"اسم الجدول في النموذج: {model._meta.db_table}", file=sys.stderr)
        print(f"اسم الجدول في النظام: {table.db_table_name}", file=sys.stderr)

        # التحقق من تطابق اسم الجدول في النموذج مع اسم الجدول في النظام
        if model._meta.db_table != table.db_table_name:
            print(f"تحذير: اسم الجدول في النموذج ({model._meta.db_table}) لا يتطابق مع اسم الجدول في النظام ({table.db_table_name})", file=sys.stderr)
            # تحديث اسم الجدول في النظام
            table.db_table_name = model._meta.db_table
            table.save()
            print(f"تم تحديث اسم الجدول في النظام إلى {table.db_table_name}", file=sys.stderr)

        # التحقق من وجود الجدول وحذفه إذا كان موجودًا
        from django.db import connection
        with connection.cursor() as cursor:
            try:
                # حذف الجدول إذا كان موجودًا
                cursor.execute(f"DROP TABLE IF EXISTS {table.db_table_name}")
                print(f"تم حذف الجدول {table.db_table_name} إذا كان موجودًا", file=sys.stderr)
            except Exception as e:
                print(f"خطأ في حذف الجدول: {str(e)}", file=sys.stderr)

        # إنشاء الجدول من جديد باستخدام Django ORM
        try:
            from django.db import connection
            from django.db.backends.base.schema import BaseDatabaseSchemaEditor

            with connection.schema_editor() as schema_editor:
                schema_editor.create_model(model)

            print(f"تم إنشاء الجدول {table.db_table_name} بنجاح باستخدام Django ORM", file=sys.stderr)
        except Exception as e:
            print(f"خطأ في إنشاء الجدول باستخدام Django ORM: {str(e)}", file=sys.stderr)

            # محاولة إنشاء الجدول باستخدام SQL مباشرة
            try:
                with connection.cursor() as cursor:
                    # إنشاء الجدول
                    cursor.execute(f"CREATE TABLE IF NOT EXISTS {table.db_table_name} (id SERIAL PRIMARY KEY)")

                    # إضافة الحقول الأساسية
                    cursor.execute(f"ALTER TABLE {table.db_table_name} ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
                    cursor.execute(f"ALTER TABLE {table.db_table_name} ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
                    cursor.execute(f"ALTER TABLE {table.db_table_name} ADD COLUMN created_by_id INTEGER NULL")

                print(f"تم إنشاء الجدول {table.db_table_name} بنجاح باستخدام SQL مباشرة", file=sys.stderr)
            except Exception as e2:
                print(f"خطأ في إنشاء الجدول باستخدام SQL مباشرة: {str(e2)}", file=sys.stderr)
                return

        # التحقق من إنشاء الجدول بنجاح
        try:
            with connection.cursor() as cursor:
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = '{table.db_table_name}'
                    )
                """)
                table_exists = cursor.fetchone()[0]
                if table_exists:
                    print(f"تم التحقق من إنشاء الجدول {table.db_table_name} بنجاح", file=sys.stderr)
                else:
                    print(f"فشل التحقق من إنشاء الجدول {table.db_table_name}", file=sys.stderr)
                    return
        except Exception as e:
            print(f"خطأ في التحقق من إنشاء الجدول: {str(e)}", file=sys.stderr)
            return

        # إضافة الحقول الموجودة إلى الجدول
        from tables.models import Field
        fields = Field.objects.filter(table=table, is_active=True).order_by('order')
        print(f"عدد الحقول الموجودة: {fields.count()}", file=sys.stderr)

        # التحقق من إضافة الحقول بنجاح
        for field in fields:
            try:
                # التحقق من وجود الحقل في الجدول
                with connection.cursor() as cursor:
                    try:
                        cursor.execute(f"SELECT {field.name} FROM {table.db_table_name} LIMIT 1")
                        print(f"الحقل {field.name} موجود بالفعل في الجدول", file=sys.stderr)
                    except Exception:
                        # إضافة الحقل إذا لم يكن موجودًا
                        DynamicModelManager.add_field_to_table(field)
                        print(f"تم إضافة الحقل {field.name} إلى الجدول", file=sys.stderr)
            except Exception as e:
                print(f"خطأ في التحقق من/إضافة الحقل {field.name}: {str(e)}", file=sys.stderr)

    @staticmethod
    def drop_table_from_database(table):
        """
        حذف جدول من قاعدة البيانات
        """
        # تنظيف النموذج من ذاكرة التطبيق
        DynamicModelManager.clean_model_from_cache(table)

        # استخدام SQL مباشرة بدلاً من schema_editor
        from django.db import connection
        with connection.cursor() as cursor:
            # حذف الجدول
            cursor.execute(f"DROP TABLE IF EXISTS {table.db_table_name}")
            print(f"تم حذف الجدول {table.db_table_name} من قاعدة البيانات")

    @staticmethod
    def add_field_to_table(field):
        """
        إضافة حقل إلى جدول في قاعدة البيانات
        """
        import sys
        print(f"إضافة الحقل {field.name} إلى الجدول {field.table.db_table_name}", file=sys.stderr)

        # تحديد نوع الحقل في PostgreSQL
        field_type_map = {
            'text': 'VARCHAR(255)',
            'textarea': 'TEXT',
            'html': 'TEXT',
            'email': 'VARCHAR(255)',
            'phone': 'VARCHAR(255)',
            'url': 'VARCHAR(255)',
            'integer': 'INTEGER',
            'decimal': 'NUMERIC(15,2)',
            'currency': 'NUMERIC(15,2)',
            'percentage': 'NUMERIC(15,2)',
            'date': 'DATE',
            'time': 'TIME',
            'datetime': 'TIMESTAMP',
            'select': 'VARCHAR(255)',
            'multiselect': 'TEXT',
            'radio': 'VARCHAR(255)',
            'checkbox': 'BOOLEAN',
            'boolean': 'BOOLEAN',
            'file': 'VARCHAR(255)',
            'image': 'VARCHAR(255)',
            'pdf': 'VARCHAR(255)',
            'document': 'VARCHAR(255)',
            'user': 'INTEGER',
            'relation': 'INTEGER'
        }

        # الحصول على نوع الحقل في PostgreSQL
        pg_type = field_type_map.get(field.field_type, 'TEXT')

        # التحقق من وجود الجدول وإضافة الحقل
        from django.db import connection
        with connection.cursor() as cursor:
            try:
                # التحقق من وجود الجدول
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = '{field.table.db_table_name}'
                    )
                """)
                table_exists = cursor.fetchone()[0]

                if not table_exists:
                    print(f"الجدول {field.table.db_table_name} غير موجود في قاعدة البيانات. سيتم إنشاؤه.", file=sys.stderr)
                    DynamicModelManager.create_table_in_database(field.table)
                    return

                # التحقق من وجود الحقل
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns
                        WHERE table_name = '{field.table.db_table_name}'
                        AND column_name = '{field.name}'
                    )
                """)
                field_exists = cursor.fetchone()[0]

                if field_exists:
                    print(f"الحقل {field.name} موجود بالفعل في الجدول {field.table.db_table_name}.", file=sys.stderr)

                    # تحديث نوع الحقل إذا تغير
                    try:
                        cursor.execute(f"""
                            SELECT data_type
                            FROM information_schema.columns
                            WHERE table_name = '{field.table.db_table_name}'
                            AND column_name = '{field.name}'
                        """)
                        current_type = cursor.fetchone()[0]

                        # تحويل نوع PostgreSQL إلى نوع مبسط للمقارنة
                        simplified_current_type = current_type.upper()
                        if simplified_current_type.startswith('CHARACTER VARYING'):
                            simplified_current_type = 'VARCHAR'
                        elif simplified_current_type == 'NUMERIC':
                            simplified_current_type = 'NUMERIC'

                        simplified_pg_type = pg_type.upper()
                        if simplified_pg_type.startswith('VARCHAR'):
                            simplified_pg_type = 'VARCHAR'

                        if simplified_current_type != simplified_pg_type:
                            print(f"تغيير نوع الحقل {field.name} من {current_type} إلى {pg_type}", file=sys.stderr)
                            try:
                                # استخدام USING للتحويل الآمن للبيانات
                                cursor.execute(f"ALTER TABLE {field.table.db_table_name} ALTER COLUMN {field.name} TYPE {pg_type} USING {field.name}::{pg_type}")
                                print(f"تم تحديث نوع الحقل {field.name} بنجاح", file=sys.stderr)
                            except Exception as e:
                                print(f"خطأ في تحديث نوع الحقل {field.name}: {str(e)}", file=sys.stderr)
                    except Exception as e:
                        print(f"خطأ في الحصول على نوع الحقل الحالي: {str(e)}", file=sys.stderr)
                else:
                    # إضافة الحقل إذا لم يكن موجودًا
                    try:
                        nullable = "NULL" if not field.is_required else "NOT NULL DEFAULT ''"
                        cursor.execute(f"ALTER TABLE {field.table.db_table_name} ADD COLUMN {field.name} {pg_type} {nullable}")
                        print(f"تم إضافة الحقل {field.name} إلى الجدول {field.table.db_table_name} بنجاح", file=sys.stderr)
                    except Exception as e:
                        print(f"خطأ في إضافة الحقل {field.name}: {str(e)}", file=sys.stderr)

                        # محاولة إضافة الحقل بطريقة أخرى إذا فشلت الطريقة الأولى
                        try:
                            # استخدام IF NOT EXISTS في PostgreSQL 11+
                            cursor.execute(f"""
                                DO $$
                                BEGIN
                                    IF NOT EXISTS (
                                        SELECT FROM information_schema.columns
                                        WHERE table_name = '{field.table.db_table_name}'
                                        AND column_name = '{field.name}'
                                    ) THEN
                                        ALTER TABLE {field.table.db_table_name} ADD COLUMN {field.name} {pg_type};
                                    END IF;
                                END
                                $$;
                            """)
                            print(f"تم إضافة الحقل {field.name} باستخدام الطريقة البديلة", file=sys.stderr)
                        except Exception as e2:
                            print(f"فشل في إضافة الحقل {field.name} بالطريقة البديلة: {str(e2)}", file=sys.stderr)

                # التحقق من إضافة الحقل بنجاح
                try:
                    cursor.execute(f"SELECT {field.name} FROM {field.table.db_table_name} LIMIT 1")
                    print(f"تم التحقق من وجود الحقل {field.name} في الجدول {field.table.db_table_name} بنجاح", file=sys.stderr)
                except Exception as e:
                    print(f"خطأ في التحقق من وجود الحقل {field.name}: {str(e)}", file=sys.stderr)

            except Exception as e:
                print(f"خطأ عام في إضافة الحقل {field.name}: {str(e)}", file=sys.stderr)

        # تنظيف النموذج من ذاكرة التطبيق وإعادة إنشائه
        DynamicModelManager.clean_model_from_cache(field.table)
        model = DynamicModelManager.get_model_for_table(field.table)
        print(f"تم إعادة إنشاء النموذج للجدول {field.table.db_table_name}", file=sys.stderr)

    @staticmethod
    def remove_field_from_table(field):
        """
        حذف حقل من جدول في قاعدة البيانات
        """
        import sys
        print(f"حذف الحقل {field.name} من الجدول {field.table.db_table_name}", file=sys.stderr)

        # ملاحظة: SQLite لا يدعم حذف الأعمدة مباشرة، لذلك نحتاج إلى إنشاء جدول جديد ونسخ البيانات

        # الخطوات:
        # 1. إنشاء جدول جديد بدون الحقل المراد حذفه
        # 2. نسخ البيانات من الجدول القديم إلى الجدول الجديد
        # 3. حذف الجدول القديم
        # 4. إعادة تسمية الجدول الجديد باسم الجدول القديم

        # الحصول على جميع الحقول النشطة باستثناء الحقل المراد حذفه
        from tables.models import Field
        fields = Field.objects.filter(table=field.table, is_active=True).exclude(id=field.id).order_by('order')

        # التحقق من وجود الجدول في قاعدة البيانات
        from django.db import connection
        with connection.cursor() as cursor:
            # التحقق من وجود الجدول بطريقة متوافقة مع PostgreSQL
            table_exists = False
            try:
                # استعلام متوافق مع PostgreSQL
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = '{field.table.db_table_name}'
                    )
                """)
                table_exists = cursor.fetchone()[0]
            except Exception as e:
                print(f"خطأ في التحقق من وجود الجدول: {str(e)}", file=sys.stderr)
                # محاولة بديلة للتحقق من وجود الجدول
                try:
                    cursor.execute(f"SELECT 1 FROM {field.table.db_table_name} LIMIT 1")
                    table_exists = True
                except:
                    table_exists = False

            if not table_exists:
                print(f"الجدول {field.table.db_table_name} غير موجود في قاعدة البيانات", file=sys.stderr)
                return

            # إنشاء جدول مؤقت بدون الحقل المراد حذفه
            temp_table_name = f"{field.table.db_table_name}_temp"

            # حذف الجدول المؤقت إذا كان موجودًا
            cursor.execute(f"DROP TABLE IF EXISTS {temp_table_name}")

            # إنشاء الجدول المؤقت - استخدام SERIAL بدلاً من AUTOINCREMENT لتوافق PostgreSQL
            cursor.execute(f"CREATE TABLE {temp_table_name} (id SERIAL PRIMARY KEY)")

            # إضافة الحقول الأساسية
            cursor.execute(f"ALTER TABLE {temp_table_name} ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            cursor.execute(f"ALTER TABLE {temp_table_name} ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            cursor.execute(f"ALTER TABLE {temp_table_name} ADD COLUMN created_by_id INTEGER NULL")

            # إضافة الحقول الأخرى
            for f in fields:
                # تحديد نوع الحقل في PostgreSQL
                field_type_map = {
                    'text': 'VARCHAR(255)',
                    'textarea': 'TEXT',
                    'html': 'TEXT',
                    'email': 'VARCHAR(255)',
                    'phone': 'VARCHAR(255)',
                    'url': 'VARCHAR(255)',
                    'integer': 'INTEGER',
                    'decimal': 'NUMERIC(15,2)',
                    'currency': 'NUMERIC(15,2)',
                    'percentage': 'NUMERIC(15,2)',
                    'date': 'DATE',
                    'time': 'TIME',
                    'datetime': 'TIMESTAMP',
                    'select': 'VARCHAR(255)',
                    'multiselect': 'TEXT',
                    'radio': 'VARCHAR(255)',
                    'checkbox': 'BOOLEAN',
                    'boolean': 'BOOLEAN',
                    'file': 'VARCHAR(255)',
                    'image': 'VARCHAR(255)',
                    'pdf': 'VARCHAR(255)',
                    'document': 'VARCHAR(255)',
                    'user': 'INTEGER',
                    'relation': 'INTEGER'
                }

                # الحصول على نوع الحقل في PostgreSQL
                pg_type = field_type_map.get(f.field_type, 'TEXT')

                # إضافة الحقل مع معالجة القيم الافتراضية للحقول العددية
                if f.field_type in ['integer', 'decimal', 'currency', 'percentage', 'user', 'relation']:
                    # للحقول العددية، نستخدم 0 كقيمة افتراضية بدلاً من ''
                    nullable = "NULL" if not f.is_required else "NOT NULL DEFAULT 0"
                else:
                    # للحقول النصية وغيرها، نستخدم '' كقيمة افتراضية
                    nullable = "NULL" if not f.is_required else "NOT NULL DEFAULT ''"

                try:
                    cursor.execute(f"ALTER TABLE {temp_table_name} ADD COLUMN {f.name} {pg_type} {nullable}")
                    print(f"تم إضافة الحقل {f.name} إلى الجدول المؤقت {temp_table_name}", file=sys.stderr)
                except Exception as e:
                    print(f"خطأ في إضافة الحقل {f.name} إلى الجدول المؤقت: {str(e)}", file=sys.stderr)
                    # محاولة إضافة الحقل بدون قيمة افتراضية
                    try:
                        cursor.execute(f"ALTER TABLE {temp_table_name} ADD COLUMN {f.name} {pg_type} NULL")
                        print(f"تم إضافة الحقل {f.name} بدون قيمة افتراضية", file=sys.stderr)
                    except Exception as e2:
                        print(f"فشل في إضافة الحقل {f.name} بدون قيمة افتراضية: {str(e2)}", file=sys.stderr)
                        # استمرار التنفيذ رغم الخطأ

            # نسخ البيانات من الجدول القديم إلى الجدول المؤقت
            # إنشاء قائمة بأسماء الحقول
            field_names = ['id', 'created_at', 'updated_at', 'created_by_id']
            select_fields = ['id', 'created_at', 'updated_at', 'created_by_id']

            # إنشاء قائمة بأسماء الحقول مع معالجة القيم الفارغة للحقول العددية
            for f in fields:
                field_names.append(f.name)

                # معالجة القيم الفارغة للحقول العددية
                if f.field_type in ['integer', 'decimal', 'currency', 'percentage', 'user', 'relation']:
                    # استخدام NULLIF لتحويل القيم الفارغة إلى NULL
                    select_fields.append(f"NULLIF({f.name}, '')")
                else:
                    select_fields.append(f.name)

            # إنشاء سلسلة بأسماء الحقول
            field_names_str = ', '.join(field_names)
            select_fields_str = ', '.join(select_fields)

            # نسخ البيانات مع معالجة القيم الفارغة
            try:
                cursor.execute(f"INSERT INTO {temp_table_name} ({field_names_str}) SELECT {select_fields_str} FROM {field.table.db_table_name}")
                print(f"تم نسخ البيانات من الجدول {field.table.db_table_name} إلى الجدول المؤقت {temp_table_name} بنجاح", file=sys.stderr)
            except Exception as e:
                print(f"خطأ في نسخ البيانات: {str(e)}", file=sys.stderr)

                # محاولة نسخ البيانات بطريقة أخرى (سجل بسجل)
                try:
                    # الحصول على جميع السجلات من الجدول القديم
                    cursor.execute(f"SELECT {field_names_str} FROM {field.table.db_table_name}")
                    rows = cursor.fetchall()

                    # إدخال كل سجل على حدة مع معالجة القيم الفارغة
                    for row in rows:
                        # إنشاء قائمة بالقيم مع استبدال القيم الفارغة بـ NULL للحقول العددية
                        values = []
                        for i, value in enumerate(row):
                            if i >= 4:  # تجاوز الحقول الأساسية (id, created_at, updated_at, created_by_id)
                                field_index = i - 4
                                if field_index < len(fields):
                                    f = fields[field_index]
                                    if f.field_type in ['integer', 'decimal', 'currency', 'percentage', 'user', 'relation'] and (value == '' or value is None):
                                        values.append('NULL')
                                    else:
                                        values.append(f"'{value}'")
                            else:
                                values.append(f"'{value}'")

                        # إدخال السجل
                        values_str = ', '.join(values)
                        cursor.execute(f"INSERT INTO {temp_table_name} ({field_names_str}) VALUES ({values_str})")

                    print(f"تم نسخ البيانات بطريقة بديلة بنجاح", file=sys.stderr)
                except Exception as e2:
                    print(f"فشل في نسخ البيانات بالطريقة البديلة: {str(e2)}", file=sys.stderr)
                    # إذا فشلت جميع المحاولات، نستمر بدون نسخ البيانات
                    print(f"سيتم الاستمرار بدون نسخ البيانات", file=sys.stderr)

            # حذف الجدول القديم
            cursor.execute(f"DROP TABLE {field.table.db_table_name}")

            # إعادة تسمية الجدول المؤقت
            cursor.execute(f"ALTER TABLE {temp_table_name} RENAME TO {field.table.db_table_name}")

            print(f"تم حذف الحقل {field.name} من الجدول {field.table.db_table_name} بنجاح", file=sys.stderr)

        # تنظيف النموذج من ذاكرة التطبيق وإعادة إنشائه
        DynamicModelManager.clean_model_from_cache(field.table)
        new_model = DynamicModelManager.get_model_for_table(field.table)
        print(f"تم إعادة إنشاء النموذج للجدول {field.table.db_table_name}", file=sys.stderr)

    @staticmethod
    def clean_model_from_cache(table):
        """
        تنظيف النموذج الديناميكي من ذاكرة التخزين المؤقت
        """
        model_name = f"DynamicModel_{table.id}"
        app_label = 'tables'
        cache_key = f"{app_label}.{model_name}"

        # حذف النموذج من ذاكرة التخزين المؤقت
        if cache_key in DynamicModelManager._models_cache:
            del DynamicModelManager._models_cache[cache_key]

        # محاولة حذف النموذج من ذاكرة التطبيق
        try:
            # الحصول على قاموس النماذج المسجلة
            app_models = apps.all_models.get(app_label, {})

            # حذف النموذج من القاموس إذا كان موجودًا
            if model_name in app_models:
                del app_models[model_name]

            # تنظيف النموذج من الذاكرة العامة
            model_key = f"{app_label}.{model_name}"
            for key in list(sys.modules.keys()):
                if key.endswith(model_key):
                    del sys.modules[key]

            print(f"تم تنظيف النموذج {model_name} من ذاكرة التطبيق")
        except Exception as e:
            print(f"خطأ أثناء تنظيف النموذج {model_name}: {str(e)}")

    @staticmethod
    def verify_table_fields(table):
        """
        التحقق من تطابق الحقول بين نموذج Django وبنية الجدول الفعلية
        """
        import sys
        print(f"التحقق من تطابق الحقول للجدول {table.db_table_name}", file=sys.stderr)

        # الحصول على الحقول المعرفة في النموذج
        from tables.models import Field
        defined_fields = Field.objects.filter(table=table, is_active=True)
        defined_field_names = set(field.name for field in defined_fields)

        # الحقول الأساسية التي يجب استبعادها من المقارنة
        core_fields = {'id', 'created_at', 'updated_at', 'created_by_id'}

        # الحصول على الحقول الفعلية في الجدول
        from django.db import connection
        actual_field_names = set()

        with connection.cursor() as cursor:
            try:
                cursor.execute(f"""
                    SELECT column_name
                    FROM information_schema.columns
                    WHERE table_name = '{table.db_table_name}'
                """)
                actual_field_names = set(row[0] for row in cursor.fetchall())
                # استبعاد الحقول الأساسية
                actual_field_names = actual_field_names - core_fields
                print(f"الحقول الفعلية في الجدول: {actual_field_names}", file=sys.stderr)
            except Exception as e:
                print(f"خطأ في الحصول على الحقول الفعلية: {str(e)}", file=sys.stderr)
                # في حالة الخطأ، نفترض أن الجدول غير موجود ونقوم بإنشائه
                DynamicModelManager.create_table_in_database(table)
                return False

        # الحقول المفقودة في الجدول الفعلي
        missing_fields = defined_field_names - actual_field_names

        # الحقول الزائدة في الجدول الفعلي (غير معرفة في النموذج)
        extra_fields = actual_field_names - defined_field_names

        # إذا كان هناك عدد كبير من الحقول المفقودة أو الزائدة، فمن الأفضل إعادة بناء الجدول بالكامل
        if len(missing_fields) > 2 or len(extra_fields) > 2:
            print(f"عدد كبير من الحقول غير المتطابقة. سيتم إعادة بناء الجدول بالكامل.", file=sys.stderr)
            return DynamicModelManager.rebuild_table(table)

        if missing_fields:
            print(f"الحقول المفقودة في الجدول الفعلي: {missing_fields}", file=sys.stderr)

            # إضافة الحقول المفقودة
            success = True
            for field_name in missing_fields:
                try:
                    field = defined_fields.get(name=field_name)
                    DynamicModelManager.add_field_to_table(field)
                    print(f"تم إضافة الحقل المفقود {field_name} إلى الجدول", file=sys.stderr)
                except Exception as e:
                    print(f"خطأ في إضافة الحقل المفقود {field_name}: {str(e)}", file=sys.stderr)
                    success = False

            # إذا فشلت إضافة أي حقل، فمن الأفضل إعادة بناء الجدول بالكامل
            if not success:
                print(f"فشل في إضافة بعض الحقول المفقودة. سيتم إعادة بناء الجدول بالكامل.", file=sys.stderr)
                return DynamicModelManager.rebuild_table(table)

        if extra_fields:
            print(f"الحقول الزائدة في الجدول الفعلي: {extra_fields}", file=sys.stderr)
            # يمكن إضافة منطق لحذف الحقول الزائدة إذا لزم الأمر

        return len(missing_fields) == 0  # True إذا كانت جميع الحقول متطابقة

    @staticmethod
    def rebuild_table(table):
        """
        إعادة بناء الجدول بالكامل مع جميع الحقول المعرفة
        """
        import sys
        print(f"إعادة بناء الجدول {table.db_table_name} بالكامل", file=sys.stderr)

        # حذف الجدول الحالي
        DynamicModelManager.drop_table_from_database(table)

        # إنشاء الجدول من جديد
        DynamicModelManager.create_table_in_database(table)

        # التحقق من إنشاء الجدول بنجاح
        from django.db import connection
        with connection.cursor() as cursor:
            try:
                cursor.execute(f"""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = '{table.db_table_name}'
                    )
                """)
                table_exists = cursor.fetchone()[0]
                if table_exists:
                    print(f"تم إعادة بناء الجدول {table.db_table_name} بنجاح", file=sys.stderr)
                    return True
                else:
                    print(f"فشل في إعادة بناء الجدول {table.db_table_name}", file=sys.stderr)
                    return False
            except Exception as e:
                print(f"خطأ في التحقق من إعادة بناء الجدول: {str(e)}", file=sys.stderr)
                return False

    @staticmethod
    def clean_all_models_for_department(department_id):
        """
        تنظيف جميع النماذج الديناميكية لقسم معين من ذاكرة التطبيق
        """
        from tables.models import Table

        # الحصول على جميع جداول القسم
        tables = Table.objects.filter(department_id=department_id)

        # تنظيف النماذج الديناميكية لكل جدول
        for table in tables:
            DynamicModelManager.clean_model_from_cache(table)

        print(f"تم تنظيف جميع النماذج الديناميكية للقسم {department_id} من ذاكرة التطبيق")
