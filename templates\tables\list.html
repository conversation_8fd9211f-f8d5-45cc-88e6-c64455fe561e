{% extends 'base.html' %}

{% block title %}الجداول - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">الجداول</h1>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الجدول</th>
                        <th>القسم</th>
                        <th>الوصف</th>
                        <th>عدد الحقول</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% if tables %}
                        {% for table in tables %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>
                                <i class="{{ table.icon }}" style="color: {{ table.color }}"></i>
                                {{ table.name }}
                            </td>
                            <td>
                                <a href="{% url 'departments:detail' table.department.id %}">
                                    {{ table.department.name }}
                                </a>
                            </td>
                            <td>{{ table.description|truncatewords:10 }}</td>
                            <td>{{ table.fields.count }}</td>
                            <td>{{ table.created_at|date:"Y-m-d" }}</td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'tables:detail' table.id %}" class="btn btn-info">
                                        <i class="fas fa-eye"></i> عرض
                                    </a>
                                    <a href="{% url 'tables:data_list' table.id %}" class="btn btn-success">
                                        <i class="fas fa-table"></i> البيانات
                                    </a>
                                    {% if user.is_superuser or table.department.created_by == user %}
                                    <a href="{% url 'tables:edit' table.id %}" class="btn btn-primary">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <a href="{% url 'tables:delete' table.id %}" class="btn btn-danger">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="7" class="text-center">لا توجد جداول متاحة.</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}
