{% extends 'base.html' %}

{% block title %}علاقات {{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">العلاقات</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="{{ table.icon }}" style="color: {{ table.color }}"></i>
        علاقات {{ table.name }}
    </h1>

    <div class="btn-group">
        <a href="{% url 'tables:relation_create' table.id %}" class="btn btn-success">
            <i class="fas fa-plus"></i> إضافة علاقة
        </a>
        <a href="{% url 'tables:detail' table.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة إلى الجدول
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if relations %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الحقل المصدر</th>
                        <th>الجدول المستهدف</th>
                        <th>نوع العلاقة</th>
                        <th>حقل العرض</th>
                        <th>الوصف</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for relation in relations %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ relation.source_field.label }}</td>
                        <td>
                            <a href="{% url 'tables:detail' relation.target_table.id %}">
                                {{ relation.target_table.name }}
                            </a>
                        </td>
                        <td>{{ relation.get_relation_type_display }}</td>
                        <td>{{ relation.display_field.label }}</td>
                        <td>{{ relation.description|truncatechars:50 }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'tables:relation_edit' table.id relation.id %}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'tables:relation_delete' table.id relation.id %}" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> لا توجد علاقات لهذا الجدول. يمكنك إنشاء علاقة جديدة بالنقر على زر "إضافة علاقة".
        </div>
        {% endif %}
    </div>
</div>

<div class="card mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">معلومات حول العلاقات</h5>
    </div>
    <div class="card-body">
        <p>العلاقات تسمح بربط الجداول ببعضها البعض. يمكنك إنشاء أنواع مختلفة من العلاقات:</p>
        
        <ul>
            <li><strong>علاقة واحد لواحد (One-to-One):</strong> كل سجل في الجدول المصدر يرتبط بسجل واحد فقط في الجدول المستهدف.</li>
            <li><strong>علاقة واحد لمتعدد (One-to-Many):</strong> كل سجل في الجدول المصدر يمكن أن يرتبط بعدة سجلات في الجدول المستهدف.</li>
            <li><strong>علاقة متعدد لمتعدد (Many-to-Many):</strong> عدة سجلات في الجدول المصدر يمكن أن ترتبط بعدة سجلات في الجدول المستهدف.</li>
        </ul>
        
        <p>عند إنشاء علاقة، يجب تحديد:</p>
        
        <ul>
            <li><strong>الحقل المصدر:</strong> الحقل في الجدول الحالي الذي سيحتوي على العلاقة.</li>
            <li><strong>الجدول المستهدف:</strong> الجدول الذي سيتم الربط به.</li>
            <li><strong>حقل العرض:</strong> الحقل في الجدول المستهدف الذي سيتم عرضه عند اختيار سجل من الجدول المستهدف.</li>
        </ul>
    </div>
</div>
{% endblock %}
