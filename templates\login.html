{% extends 'base.html' %}

{% block title %}تسجيل الدخول - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white text-center">
                <h3 class="mb-0">تسجيل الدخول</h3>
            </div>
            <div class="card-body">
                {% if form.errors %}
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> اسم المستخدم أو كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.
                </div>
                {% endif %}
                
                <form method="post" action="{% url 'login' %}">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="id_username" class="form-label">اسم المستخدم</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                            <input type="text" name="username" id="id_username" class="form-control" placeholder="أدخل اسم المستخدم" required autofocus>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_password" class="form-label">كلمة المرور</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" name="password" id="id_password" class="form-control" placeholder="أدخل كلمة المرور" required>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                        <label class="form-check-label" for="remember_me">تذكرني</label>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                        </button>
                    </div>
                    
                    <input type="hidden" name="next" value="{{ next }}">
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
