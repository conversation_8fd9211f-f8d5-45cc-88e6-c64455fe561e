{% extends 'base.html' %}
{% load table_tags %}

{% block title %}{{ title }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:data_list' table.id %}">البيانات</a></li>
        {% if record %}
        <li class="breadcrumb-item active" aria-current="page">تعديل سجل</li>
        {% else %}
        <li class="breadcrumb-item active" aria-current="page">إضافة سجل جديد</li>
        {% endif %}
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{{ title }}</h5>
    </div>
    <div class="card-body">
        <form method="post" class="needs-validation" novalidate enctype="multipart/form-data">
            {% csrf_token %}

            {% for field in form %}
            <div class="mb-3">
                {% if field.field.widget.input_type == 'checkbox' %}
                <div class="form-check">
                    {{ field }}
                    <label for="{{ field.id_for_label }}" class="form-check-label">{{ field.label }}</label>
                    {% if field.help_text %}
                    <small class="form-text text-muted">{{ field.help_text }}</small>
                    {% endif %}
                    {% if field.errors %}
                    <div class="invalid-feedback">
                        {% for error in field.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                {% else %}
                <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                {{ field }}
                {% if field.help_text %}
                <small class="form-text text-muted">{{ field.help_text }}</small>
                {% endif %}
                {% if field.errors %}
                <div class="invalid-feedback">
                    {% for error in field.errors %}{{ error }}{% endfor %}
                </div>
                {% endif %}
                {% endif %}
            </div>
            {% endfor %}

            <div class="d-flex justify-content-between">
                <a href="{% url 'tables:data_list' table.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تطبيق الأنماط على حقول النموذج
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة الأنماط إلى حقول النموذج
        document.querySelectorAll('input, select, textarea').forEach(function(element) {
            if (element.type !== 'checkbox' && element.type !== 'radio') {
                element.classList.add('form-control');

                if (element.required) {
                    element.classList.add('is-required');
                }

                if (element.classList.contains('is-invalid')) {
                    element.classList.add('is-invalid');
                }
            }

            // إضافة أنماط خاصة لأنواع معينة من الحقول
            if (element.type === 'date' || element.type === 'datetime-local') {
                element.classList.add('date-input');
            }

            if (element.type === 'file') {
                element.classList.add('form-control-file');
            }

            if (element.tagName === 'SELECT') {
                element.classList.add('form-select');
            }
        });

        // تفعيل محرر النصوص المنسق
        document.querySelectorAll('textarea.html-editor').forEach(function(editor) {
            // يمكن إضافة كود لتفعيل محرر HTML هنا
            // مثال: CKEDITOR.replace(editor.id);
        });

        // تفعيل التحقق من صحة النموذج
        const forms = document.querySelectorAll('.needs-validation');
        Array.from(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    });
</script>
{% endblock %}
