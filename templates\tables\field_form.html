{% extends 'base.html' %}

{% block title %}{{ title }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:field_list' table.id %}">الحقول</a></li>
        {% if field %}
        <li class="breadcrumb-item active" aria-current="page">تعديل {{ field.label }}</li>
        {% else %}
        <li class="breadcrumb-item active" aria-current="page">إضافة حقل جديد</li>
        {% endif %}
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{{ title }}</h5>
    </div>
    <div class="card-body">
        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="id_name" class="form-label">اسم الحقل (بالإنجليزية بدون مسافات)</label>
                    <input type="text" name="name" id="id_name" class="form-control {% if form.name.errors %}is-invalid{% endif %}" value="{{ form.name.value|default:'' }}" required {% if field %}readonly{% endif %}>
                    <small class="form-text text-muted">يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطة سفلية فقط.</small>
                    {% if form.name.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.name.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-6">
                    <label for="id_label" class="form-label">عنوان الحقل</label>
                    <input type="text" name="label" id="id_label" class="form-control {% if form.label.errors %}is-invalid{% endif %}" value="{{ form.label.value|default:'' }}" required>
                    {% if form.label.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.label.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="id_field_type" class="form-label">نوع الحقل</label>
                    <select name="field_type" id="id_field_type" class="form-control {% if form.field_type.errors %}is-invalid{% endif %}" {% if field %}disabled{% endif %}>
                        <optgroup label="نصوص">
                            <option value="text" {% if form.field_type.value == 'text' %}selected{% endif %}>نص قصير</option>
                            <option value="textarea" {% if form.field_type.value == 'textarea' %}selected{% endif %}>نص طويل</option>
                            <option value="html" {% if form.field_type.value == 'html' %}selected{% endif %}>نص منسق (HTML)</option>
                            <option value="email" {% if form.field_type.value == 'email' %}selected{% endif %}>بريد إلكتروني</option>
                            <option value="phone" {% if form.field_type.value == 'phone' %}selected{% endif %}>رقم هاتف</option>
                            <option value="url" {% if form.field_type.value == 'url' %}selected{% endif %}>رابط URL</option>
                        </optgroup>
                        <optgroup label="أرقام">
                            <option value="integer" {% if form.field_type.value == 'integer' %}selected{% endif %}>عدد صحيح</option>
                            <option value="decimal" {% if form.field_type.value == 'decimal' %}selected{% endif %}>عدد عشري</option>
                            <option value="currency" {% if form.field_type.value == 'currency' %}selected{% endif %}>عملة</option>
                            <option value="percentage" {% if form.field_type.value == 'percentage' %}selected{% endif %}>نسبة مئوية</option>
                        </optgroup>
                        <optgroup label="تواريخ وأوقات">
                            <option value="date" {% if form.field_type.value == 'date' %}selected{% endif %}>تاريخ</option>
                            <option value="time" {% if form.field_type.value == 'time' %}selected{% endif %}>وقت</option>
                            <option value="datetime" {% if form.field_type.value == 'datetime' %}selected{% endif %}>تاريخ ووقت</option>
                        </optgroup>
                        <optgroup label="اختيارات">
                            <option value="select" {% if form.field_type.value == 'select' %}selected{% endif %}>قائمة منسدلة</option>
                            <option value="multiselect" {% if form.field_type.value == 'multiselect' %}selected{% endif %}>اختيار متعدد</option>
                            <option value="radio" {% if form.field_type.value == 'radio' %}selected{% endif %}>زر راديو</option>
                            <option value="checkbox" {% if form.field_type.value == 'checkbox' %}selected{% endif %}>صندوق اختيار</option>
                        </optgroup>
                        <optgroup label="ملفات">
                            <option value="file" {% if form.field_type.value == 'file' %}selected{% endif %}>ملف عام</option>
                            <option value="image" {% if form.field_type.value == 'image' %}selected{% endif %}>صورة</option>
                            <option value="pdf" {% if form.field_type.value == 'pdf' %}selected{% endif %}>ملف PDF</option>
                            <option value="document" {% if form.field_type.value == 'document' %}selected{% endif %}>مستند</option>
                        </optgroup>
                        <optgroup label="أخرى">
                            <option value="boolean" {% if form.field_type.value == 'boolean' %}selected{% endif %}>نعم/لا</option>
                            <option value="user" {% if form.field_type.value == 'user' %}selected{% endif %}>مستخدم</option>
                            <option value="relation" {% if form.field_type.value == 'relation' %}selected{% endif %}>علاقة بجدول آخر</option>
                        </optgroup>
                    </select>
                    {% if field %}
                    <input type="hidden" name="field_type" value="{{ form.field_type.value }}">
                    {% endif %}
                    {% if form.field_type.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.field_type.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-6">
                    <label for="id_order" class="form-label">الترتيب</label>
                    <input type="number" name="order" id="id_order" class="form-control {% if form.order.errors %}is-invalid{% endif %}" value="{{ form.order.value|default:'0' }}">
                    {% if form.order.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.order.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="mb-3">
                <label for="id_description" class="form-label">وصف الحقل</label>
                <textarea name="description" id="id_description" class="form-control {% if form.description.errors %}is-invalid{% endif %}" rows="2">{{ form.description.value|default:'' }}</textarea>
                {% if form.description.errors %}
                <div class="invalid-feedback">
                    {% for error in form.description.errors %}{{ error }}{% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="id_default_value" class="form-label">القيمة الافتراضية</label>
                    <input type="text" name="default_value" id="id_default_value" class="form-control {% if form.default_value.errors %}is-invalid{% endif %}" value="{{ form.default_value.value|default:'' }}">
                    {% if form.default_value.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.default_value.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-6">
                    <label for="id_placeholder" class="form-label">نص توضيحي</label>
                    <input type="text" name="placeholder" id="id_placeholder" class="form-control {% if form.placeholder.errors %}is-invalid{% endif %}" value="{{ form.placeholder.value|default:'' }}">
                    {% if form.placeholder.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.placeholder.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="id_help_text" class="form-label">نص المساعدة</label>
                    <input type="text" name="help_text" id="id_help_text" class="form-control {% if form.help_text.errors %}is-invalid{% endif %}" value="{{ form.help_text.value|default:'' }}">
                    {% if form.help_text.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.help_text.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-12">
                    <label for="id_options" class="form-label">خيارات الحقل</label>
                    <textarea name="options" id="id_options" class="form-control {% if form.options.errors %}is-invalid{% endif %}" rows="3">{{ form.options.value|default:'' }}</textarea>
                    <small class="form-text text-muted">للحقول ذات الاختيارات (قائمة منسدلة، اختيار متعدد، زر راديو). يمكن إدخال القيم مفصولة بفواصل أو بتنسيق JSON.</small>
                    {% if form.options.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.options.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="id_validation_regex" class="form-label">تعبير منتظم للتحقق</label>
                    <input type="text" name="validation_regex" id="id_validation_regex" class="form-control {% if form.validation_regex.errors %}is-invalid{% endif %}" value="{{ form.validation_regex.value|default:'' }}">
                    {% if form.validation_regex.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.validation_regex.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-3">
                    <label for="id_min_value" class="form-label">القيمة الدنيا</label>
                    <input type="text" name="min_value" id="id_min_value" class="form-control {% if form.min_value.errors %}is-invalid{% endif %}" value="{{ form.min_value.value|default:'' }}">
                    {% if form.min_value.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.min_value.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-3">
                    <label for="id_max_value" class="form-label">القيمة القصوى</label>
                    <input type="text" name="max_value" id="id_max_value" class="form-control {% if form.max_value.errors %}is-invalid{% endif %}" value="{{ form.max_value.value|default:'' }}">
                    {% if form.max_value.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.max_value.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-3">
                    <div class="form-check">
                        <input type="checkbox" name="is_required" id="id_is_required" class="form-check-input {% if form.is_required.errors %}is-invalid{% endif %}" {% if form.is_required.value %}checked{% endif %}>
                        <label for="id_is_required" class="form-check-label">مطلوب</label>
                        {% if form.is_required.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.is_required.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="form-check">
                        <input type="checkbox" name="is_unique" id="id_is_unique" class="form-check-input {% if form.is_unique.errors %}is-invalid{% endif %}" {% if form.is_unique.value %}checked{% endif %}>
                        <label for="id_is_unique" class="form-check-label">فريد</label>
                        {% if form.is_unique.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.is_unique.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="form-check">
                        <input type="checkbox" name="is_indexed" id="id_is_indexed" class="form-check-input {% if form.is_indexed.errors %}is-invalid{% endif %}" {% if form.is_indexed.value %}checked{% endif %}>
                        <label for="id_is_indexed" class="form-check-label">مفهرس</label>
                        {% if form.is_indexed.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.is_indexed.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="form-check">
                        <input type="checkbox" name="is_searchable" id="id_is_searchable" class="form-check-input {% if form.is_searchable.errors %}is-invalid{% endif %}" {% if form.is_searchable.value %}checked{% endif %}>
                        <label for="id_is_searchable" class="form-check-label">قابل للبحث</label>
                        {% if form.is_searchable.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.is_searchable.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-12">
                    <div class="form-check">
                        <input type="checkbox" name="is_active" id="id_is_active" class="form-check-input {% if form.is_active.errors %}is-invalid{% endif %}" {% if form.is_active.value %}checked{% endif %}>
                        <label for="id_is_active" class="form-check-label">نشط</label>
                        {% if form.is_active.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.is_active.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% url 'tables:field_list' table.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> حفظ
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Show/hide options based on field type
    document.getElementById('id_field_type').addEventListener('change', function() {
        const fieldType = this.value;
        const optionsField = document.getElementById('id_options').closest('.row');
        const validationRegexField = document.getElementById('id_validation_regex').closest('.row');
        const minMaxFields = document.getElementById('id_min_value').closest('.row');
        
        // Reset visibility
        optionsField.style.display = 'none';
        validationRegexField.style.display = 'none';
        minMaxFields.style.display = 'none';
        
        // Show relevant fields based on type
        if (['select', 'multiselect', 'radio', 'checkbox'].includes(fieldType)) {
            optionsField.style.display = 'flex';
        }
        
        if (['text', 'textarea', 'email', 'phone', 'url'].includes(fieldType)) {
            validationRegexField.style.display = 'flex';
        }
        
        if (['integer', 'decimal', 'currency', 'percentage', 'date', 'time', 'datetime'].includes(fieldType)) {
            minMaxFields.style.display = 'flex';
        }
    });
    
    // Trigger change event on load
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('id_field_type').dispatchEvent(new Event('change'));
    });
</script>
{% endblock %}
