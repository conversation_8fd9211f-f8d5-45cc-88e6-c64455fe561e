{% extends 'base.html' %}

{% block title %}خطأ في الجدول {{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">خطأ في الجدول</li>
    </ol>
</nav>

<div class="alert alert-danger">
    <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> حدث خطأ في الجدول!</h4>
    <p>{{ error }}</p>
    <hr>
    <p class="mb-0">يمكنك محاولة إصلاح الجدول بالضغط على الزر أدناه.</p>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">معلومات الجدول</h5>
    </div>
    <div class="card-body">
        <dl class="row">
            <dt class="col-sm-3">اسم الجدول:</dt>
            <dd class="col-sm-9">{{ table.name }}</dd>
            
            <dt class="col-sm-3">اسم الجدول في قاعدة البيانات:</dt>
            <dd class="col-sm-9">{{ table.db_table_name }}</dd>
            
            <dt class="col-sm-3">القسم:</dt>
            <dd class="col-sm-9">{{ department.name }}</dd>
            
            <dt class="col-sm-3">عدد الحقول:</dt>
            <dd class="col-sm-9">{{ fields.count }}</dd>
        </dl>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">الحقول المعرفة في الجدول</h5>
    </div>
    <div class="card-body">
        {% if fields %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الحقل</th>
                        <th>الاسم المعروض</th>
                        <th>نوع الحقل</th>
                        <th>مطلوب</th>
                    </tr>
                </thead>
                <tbody>
                    {% for field in fields %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ field.name }}</td>
                        <td>{{ field.label }}</td>
                        <td>{{ field.field_type }}</td>
                        <td>
                            {% if field.is_required %}
                            <span class="badge bg-success"><i class="fas fa-check"></i> نعم</span>
                            {% else %}
                            <span class="badge bg-secondary"><i class="fas fa-times"></i> لا</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p class="text-center">لا توجد حقول معرفة في هذا الجدول.</p>
        {% endif %}
    </div>
</div>

<div class="d-flex justify-content-between">
    <a href="{% url 'tables:detail' table.id %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right"></i> العودة إلى الجدول
    </a>
    
    <a href="{% url 'tables:fix_table' table.id %}" class="btn btn-danger">
        <i class="fas fa-tools"></i> إصلاح الجدول
    </a>
</div>
{% endblock %}
