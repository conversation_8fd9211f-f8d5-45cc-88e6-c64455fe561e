from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from departments.models import Department
from datetime import datetime

def home(request):
    """
    عرض الصفحة الرئيسية
    """
    # الحصول على الأقسام المتاحة للمستخدم الحالي
    departments = []
    if request.user.is_authenticated:
        departments = Department.objects.all()
        # يمكن إضافة منطق لتصفية الأقسام حسب صلاحيات المستخدم هنا
    
    context = {
        'departments': departments,
        'current_year': datetime.now().year
    }
    
    return render(request, 'home.html', context)
