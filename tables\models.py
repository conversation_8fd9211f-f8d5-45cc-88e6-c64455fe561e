from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from django.utils.text import slugify
from departments.models import Department
import uuid
import json

class Table(models.Model):
    """
    نموذج الجدول
    """
    VISIBILITY_CHOICES = (
        ('public', 'عام'),
        ('private', 'خاص'),
        ('inherit', 'وراثة من القسم'),
    )

    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name="tables", verbose_name="القسم")
    name = models.CharField(max_length=100, verbose_name="اسم الجدول")
    slug = models.SlugField(max_length=120, blank=True, verbose_name="الاسم اللطيف")
    description = models.TextField(blank=True, verbose_name="وصف الجدول")
    db_table_name = models.CharField(max_length=100, unique=True, verbose_name="اسم الجدول في قاعدة البيانات")
    icon = models.CharField(max_length=50, default="fas fa-table", verbose_name="أيقونة الجدول")
    color = models.CharField(max_length=20, default="#0d6efd", verbose_name="لون الجدول")
    order = models.PositiveIntegerField(default=0, verbose_name="الترتيب")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    visibility = models.CharField(max_length=10, choices=VISIBILITY_CHOICES, default='inherit', verbose_name="الرؤية")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="created_tables", verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "جدول"
        verbose_name_plural = "جداول"
        ordering = ['department', 'order', 'name']
        unique_together = ('department', 'slug')

    def __str__(self):
        return f"{self.name} ({self.department.name})"

    def save(self, *args, **kwargs):
        # إنشاء slug فريد للجدول
        if not self.slug or self.slug.strip() == '':
            # استخدام معرف فريد بدلاً من الاعتماد على slugify للأسماء العربية
            unique_id = uuid.uuid4().hex[:8]
            self.slug = f"table-{unique_id}"

        # التأكد من أن الـ slug فريد ضمن نفس القسم
        base_slug = self.slug
        counter = 1
        while Table.objects.filter(department=self.department, slug=self.slug).exclude(id=self.id).exists():
            self.slug = f"{base_slug}-{counter}"
            counter += 1

        if not self.db_table_name:
            # إنشاء اسم جدول فريد لقاعدة البيانات
            prefix = 'dept_data'
            unique_id = uuid.uuid4().hex[:8]
            self.db_table_name = f"{prefix}_{self.department.id}_{unique_id}"

        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('tables:detail', args=[self.id])

class Field(models.Model):
    """
    نموذج الحقل
    """
    FIELD_TYPES = (
        # نصوص
        ('text', 'نص قصير'),
        ('textarea', 'نص طويل'),
        ('html', 'نص منسق (HTML)'),
        ('email', 'بريد إلكتروني'),
        ('phone', 'رقم هاتف'),
        ('url', 'رابط URL'),

        # أرقام
        ('integer', 'عدد صحيح'),
        ('decimal', 'عدد عشري'),
        ('currency', 'عملة'),
        ('percentage', 'نسبة مئوية'),

        # تواريخ وأوقات
        ('date', 'تاريخ'),
        ('time', 'وقت'),
        ('datetime', 'تاريخ ووقت'),

        # اختيارات
        ('select', 'قائمة منسدلة'),
        ('multiselect', 'اختيار متعدد'),
        ('radio', 'زر راديو'),
        ('checkbox', 'صندوق اختيار'),

        # ملفات
        ('file', 'ملف عام'),
        ('image', 'صورة'),
        ('pdf', 'ملف PDF'),
        ('document', 'مستند'),

        # أخرى
        ('boolean', 'نعم/لا'),
        ('user', 'مستخدم'),
        ('relation', 'علاقة بجدول آخر'),
    )

    table = models.ForeignKey(Table, on_delete=models.CASCADE, related_name="fields", verbose_name="الجدول")
    name = models.CharField(max_length=100, verbose_name="اسم الحقل")
    label = models.CharField(max_length=100, verbose_name="عنوان الحقل")
    field_type = models.CharField(max_length=20, choices=FIELD_TYPES, verbose_name="نوع الحقل")
    description = models.TextField(blank=True, verbose_name="وصف الحقل")
    is_required = models.BooleanField(default=False, verbose_name="مطلوب")
    is_unique = models.BooleanField(default=False, verbose_name="فريد")
    is_indexed = models.BooleanField(default=False, verbose_name="مفهرس")
    is_searchable = models.BooleanField(default=True, verbose_name="قابل للبحث")
    default_value = models.TextField(blank=True, null=True, verbose_name="القيمة الافتراضية")
    placeholder = models.CharField(max_length=200, blank=True, verbose_name="نص توضيحي")
    help_text = models.CharField(max_length=200, blank=True, verbose_name="نص المساعدة")
    order = models.PositiveIntegerField(default=0, verbose_name="الترتيب")
    options = models.TextField(blank=True, null=True, verbose_name="خيارات الحقل")
    validation_regex = models.CharField(max_length=500, blank=True, verbose_name="تعبير منتظم للتحقق")
    min_value = models.CharField(max_length=50, blank=True, null=True, verbose_name="القيمة الدنيا")
    max_value = models.CharField(max_length=50, blank=True, null=True, verbose_name="القيمة القصوى")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "حقل"
        verbose_name_plural = "حقول"
        ordering = ['table', 'order', 'name']
        unique_together = ('table', 'name')

    def __str__(self):
        return f"{self.label} ({self.table.name})"

    def get_options_list(self):
        """
        الحصول على قائمة الخيارات للحقول ذات الاختيارات
        """
        if not self.options:
            return []

        try:
            return json.loads(self.options)
        except:
            # إذا لم يكن JSON صالحًا، افترض أنه قائمة مفصولة بفواصل
            return [option.strip() for option in self.options.split(',') if option.strip()]

class TableRelation(models.Model):
    """
    نموذج العلاقة بين الجداول
    """
    RELATION_TYPES = (
        ('one_to_one', 'واحد لواحد'),
        ('one_to_many', 'واحد لمتعدد'),
        ('many_to_many', 'متعدد لمتعدد'),
    )

    source_table = models.ForeignKey(Table, on_delete=models.CASCADE, related_name="source_relations", verbose_name="الجدول المصدر")
    target_table = models.ForeignKey(Table, on_delete=models.CASCADE, related_name="target_relations", verbose_name="الجدول الهدف")
    source_field = models.ForeignKey(Field, on_delete=models.CASCADE, related_name="source_relations", verbose_name="الحقل المصدر")
    relation_type = models.CharField(max_length=20, choices=RELATION_TYPES, verbose_name="نوع العلاقة")
    name = models.CharField(max_length=100, verbose_name="اسم العلاقة")
    description = models.TextField(blank=True, verbose_name="وصف العلاقة")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")

    class Meta:
        verbose_name = "علاقة بين الجداول"
        verbose_name_plural = "علاقات بين الجداول"
        unique_together = ('source_table', 'target_table', 'source_field')

    def __str__(self):
        return f"{self.name}: {self.source_table.name} -> {self.target_table.name}"
