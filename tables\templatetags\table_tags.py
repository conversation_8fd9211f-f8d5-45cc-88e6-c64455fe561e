from django import template

register = template.Library()

@register.filter
def getattribute(obj, attr):
    """
    فلتر مخصص للحصول على قيمة خاصية من كائن
    مثال: {{ object|getattribute:'property_name' }}
    """
    # طباعة معلومات تشخيصية
    import sys
    print(f"getattribute: الكائن: {type(obj)}, الخاصية: {attr}", file=sys.stderr)

    # التحقق من وجود الكائن
    if obj is None:
        print(f"  الكائن فارغ (None)", file=sys.stderr)
        return None

    # محاولة الحصول على القيمة بطرق مختلفة
    value = None

    # الطريقة 1: استخدام __getattribute__
    try:
        value = obj.__getattribute__(attr)
        print(f"  تم الحصول على القيمة باستخدام __getattribute__: {value}", file=sys.stderr)
        return value
    except (AttributeError, TypeError) as e:
        print(f"  خطأ في __getattribute__: {str(e)}", file=sys.stderr)

    # الطريقة 2: استخدام getattr
    try:
        value = getattr(obj, attr, None)
        if value is not None:
            print(f"  تم الحصول على القيمة باستخدام getattr: {value}", file=sys.stderr)
            return value
    except (AttributeError, TypeError) as e:
        print(f"  خطأ في getattr: {str(e)}", file=sys.stderr)

    # الطريقة 3: استخدام الوصول إلى القاموس
    try:
        value = obj[attr]
        print(f"  تم الحصول على القيمة باستخدام الوصول إلى القاموس: {value}", file=sys.stderr)
        return value
    except (KeyError, TypeError) as e:
        print(f"  خطأ في الوصول إلى القاموس: {str(e)}", file=sys.stderr)

    # الطريقة 4: محاولة الوصول إلى الحقل باستخدام اسم مختلف (مثل count_field -> id_count)
    if attr == 'count_field':
        alternative_names = ['id_count', 'count_id', 'counter']
        for alt_name in alternative_names:
            try:
                value = getattr(obj, alt_name, None)
                if value is not None:
                    print(f"  تم الحصول على القيمة باستخدام اسم بديل {alt_name}: {value}", file=sys.stderr)
                    return value
            except (AttributeError, TypeError):
                pass

    # إذا وصلنا إلى هنا، فلم نتمكن من الحصول على القيمة
    print(f"  لم يتم العثور على القيمة", file=sys.stderr)
    return None
