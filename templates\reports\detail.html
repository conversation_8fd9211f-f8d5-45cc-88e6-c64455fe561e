{% extends 'base.html' %}
{% load table_tags %}

{% block title %}{{ report.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:list' %}">التقارير</a></li>
        <li class="breadcrumb-item active" aria-current="page">{{ report.name }}</li>
    </ol>
</nav>

<div class="card mb-4">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">{{ report.name }}</h5>
        <div>
            {% if is_admin %}
            <a href="{% url 'reports:edit' report.id %}" class="btn btn-light btn-sm">
                <i class="fas fa-edit"></i> تعديل
            </a>
            <a href="{% url 'reports:delete' report.id %}" class="btn btn-danger btn-sm">
                <i class="fas fa-trash"></i> حذف
            </a>
            {% endif %}
            <a href="{% url 'reports:export' report.id %}" class="btn btn-success btn-sm">
                <i class="fas fa-file-export"></i> تصدير
            </a>
            <a href="{% url 'reports:print' report.id %}" class="btn btn-secondary btn-sm" target="_blank">
                <i class="fas fa-print"></i> طباعة
            </a>
        </div>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <dl class="row">
                    <dt class="col-sm-4">القسم:</dt>
                    <dd class="col-sm-8">{{ report.department.name }}</dd>

                    <dt class="col-sm-4">الجدول:</dt>
                    <dd class="col-sm-8">{{ report.table.name }}</dd>

                    <dt class="col-sm-4">نوع التقرير:</dt>
                    <dd class="col-sm-8">{{ report.get_report_type_display }}</dd>
                </dl>
            </div>
            <div class="col-md-6">
                <dl class="row">
                    <dt class="col-sm-4">تاريخ الإنشاء:</dt>
                    <dd class="col-sm-8">{{ report.created_at|date:"Y-m-d H:i" }}</dd>

                    <dt class="col-sm-4">بواسطة:</dt>
                    <dd class="col-sm-8">{{ report.created_by.get_full_name|default:report.created_by.username }}</dd>

                    <dt class="col-sm-4">حالة التقرير:</dt>
                    <dd class="col-sm-8">
                        {% if report.is_active %}
                        <span class="badge bg-success">نشط</span>
                        {% else %}
                        <span class="badge bg-secondary">غير نشط</span>
                        {% endif %}
                        {% if report.is_public %}
                        <span class="badge bg-info">عام</span>
                        {% else %}
                        <span class="badge bg-warning">خاص</span>
                        {% endif %}
                    </dd>
                </dl>
            </div>
        </div>

        {% if report.description %}
        <div class="mt-3">
            <h6>وصف التقرير:</h6>
            <p>{{ report.description }}</p>
        </div>
        {% endif %}
    </div>
</div>

{% if report_filters %}
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">فلاتر التقرير</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-sm table-bordered">
                <thead>
                    <tr>
                        <th>الحقل</th>
                        <th>العملية</th>
                        <th>القيمة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for filter in report_filters %}
                    <tr>
                        <td>{{ filter.field.label }}</td>
                        <td>{{ filter.get_operator_display }}</td>
                        <td>
                            {% if filter.operator == 'between' %}
                            {{ filter.value }} - {{ filter.value2 }}
                            {% elif filter.operator == 'is_null' or filter.operator == 'is_not_null' %}
                            -
                            {% else %}
                            {{ filter.value }}
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

{% if report.report_type == 'chart' and chart_data %}
<div class="card mb-4">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">الرسم البياني</h5>
    </div>
    <div class="card-body">
        <canvas id="reportChart" width="400" height="200"></canvas>
    </div>
</div>
{% endif %}

<div class="card">
    <div class="card-header bg-secondary text-white">
        <h5 class="mb-0">بيانات التقرير</h5>
    </div>
    <div class="card-body">
        {% if records %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        {% for field in report_fields %}
                        {% if field.is_visible %}
                        <th>{{ field.label|default:field.field.label }}</th>
                        {% endif %}
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for record in records %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        {% for field in report_fields %}
                        {% if field.is_visible %}
                        <td>
                            {% with value=record|getattribute:field.field.name %}
                            {% if field.field.field_type == 'boolean' %}
                                {% if value %}
                                <span class="badge bg-success"><i class="fas fa-check"></i> نعم</span>
                                {% else %}
                                <span class="badge bg-secondary"><i class="fas fa-times"></i> لا</span>
                                {% endif %}
                            {% elif field.field.field_type == 'date' %}
                                {{ value|date:"Y-m-d" }}
                            {% elif field.field.field_type == 'time' %}
                                {{ value|time:"H:i" }}
                            {% elif field.field.field_type == 'datetime' %}
                                {{ value|date:"Y-m-d H:i" }}
                            {% else %}
                                {{ value|default:"-" }}
                            {% endif %}
                            {% endwith %}
                        </td>
                        {% endif %}
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> لا توجد بيانات متاحة لهذا التقرير.
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if report.report_type == 'chart' and chart_data %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var ctx = document.getElementById('reportChart').getContext('2d');
        var chartData = {{ chart_data|safe }};
        
        var myChart = new Chart(ctx, {
            type: chartData.type,
            data: {
                labels: chartData.labels,
                datasets: chartData.datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    });
</script>
{% endif %}
{% endblock %}
