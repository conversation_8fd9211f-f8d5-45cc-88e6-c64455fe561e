{% extends 'base.html' %}

{% block title %}{{ department.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item active" aria-current="page">{{ department.name }}</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="{{ department.icon }}" style="color: {{ department.color }}"></i>
        {{ department.name }}
    </h1>
    
    <div class="btn-group">
        {% if is_admin %}
        <a href="{% url 'departments:edit' department.id %}" class="btn btn-outline-primary">
            <i class="fas fa-edit"></i> تعديل
        </a>
        <a href="{% url 'departments:permissions' department.id %}" class="btn btn-outline-secondary">
            <i class="fas fa-lock"></i> الصلاحيات
        </a>
        <a href="{% url 'departments:delete' department.id %}" class="btn btn-outline-danger">
            <i class="fas fa-trash"></i> حذف
        </a>
        {% endif %}
    </div>
</div>

{% if department.description %}
<div class="card mb-4">
    <div class="card-body">
        <h5 class="card-title">وصف القسم</h5>
        <p class="card-text">{{ department.description }}</p>
    </div>
</div>
{% endif %}

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">الجداول</h5>
                {% if is_admin %}
                <a href="{% url 'tables:create' department.id %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> إنشاء جدول جديد
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if tables %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم الجدول</th>
                                <th>الوصف</th>
                                <th>عدد الحقول</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for table in tables %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>
                                    <i class="{{ table.icon }}" style="color: {{ table.color }}"></i>
                                    {{ table.name }}
                                </td>
                                <td>{{ table.description|truncatewords:10 }}</td>
                                <td>{{ table.fields.count }}</td>
                                <td>{{ table.created_at|date:"Y-m-d" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'tables:detail' table.id %}" class="btn btn-info">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                        <a href="{% url 'tables:data_list' table.id %}" class="btn btn-success">
                                            <i class="fas fa-table"></i> البيانات
                                        </a>
                                        {% if is_admin %}
                                        <a href="{% url 'tables:edit' table.id %}" class="btn btn-primary">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <a href="{% url 'tables:delete' table.id %}" class="btn btn-danger">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> لا توجد جداول في هذا القسم.
                    {% if is_admin %}
                    <a href="{% url 'tables:create' department.id %}" class="alert-link">إنشاء جدول جديد</a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">التقارير</h5>
                {% if is_admin %}
                <a href="{% url 'reports:create' department.id %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> إنشاء تقرير جديد
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if department.reports.all %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم التقرير</th>
                                <th>الجدول</th>
                                <th>النوع</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in department.reports.all %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ report.name }}</td>
                                <td>{{ report.table.name }}</td>
                                <td>{{ report.get_report_type_display }}</td>
                                <td>{{ report.created_at|date:"Y-m-d" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'reports:detail' report.id %}" class="btn btn-info">
                                            <i class="fas fa-eye"></i> عرض
                                        </a>
                                        {% if is_admin %}
                                        <a href="{% url 'reports:edit' report.id %}" class="btn btn-primary">
                                            <i class="fas fa-edit"></i> تعديل
                                        </a>
                                        <a href="{% url 'reports:delete' report.id %}" class="btn btn-danger">
                                            <i class="fas fa-trash"></i> حذف
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> لا توجد تقارير في هذا القسم.
                    {% if is_admin %}
                    <a href="{% url 'reports:create' department.id %}" class="alert-link">إنشاء تقرير جديد</a>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
