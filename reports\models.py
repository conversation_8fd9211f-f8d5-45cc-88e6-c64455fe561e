from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from departments.models import Department
from tables.models import Table, Field
import json

class Report(models.Model):
    """
    نموذج التقرير
    """
    REPORT_TYPES = (
        ('table', 'جدولي'),
        ('chart', 'رسم بياني'),
        ('summary', 'ملخص'),
        ('custom', 'مخصص'),
    )

    CHART_TYPES = (
        ('bar', 'شريطي'),
        ('line', 'خطي'),
        ('pie', 'دائري'),
        ('area', 'مساحي'),
        ('scatter', 'نقطي'),
        ('radar', 'راداري'),
        ('heatmap', 'خريطة حرارية'),
    )

    name = models.CharField(max_length=100, verbose_name="اسم التقرير")
    description = models.TextField(blank=True, verbose_name="وصف التقرير")
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name="reports", verbose_name="القسم")
    table = models.ForeignKey(Table, on_delete=models.CASCADE, related_name="reports", verbose_name="الجدول")
    report_type = models.CharField(max_length=20, choices=REPORT_TYPES, default='table', verbose_name="نوع التقرير")
    chart_type = models.CharField(max_length=20, choices=CHART_TYPES, blank=True, null=True, verbose_name="نوع الرسم البياني")
    is_public = models.BooleanField(default=False, verbose_name="عام")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    config = models.TextField(blank=True, null=True, verbose_name="إعدادات التقرير")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name="created_reports", verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "تقرير"
        verbose_name_plural = "تقارير"
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('reports:detail', args=[self.id])

    def get_config_dict(self):
        """
        الحصول على إعدادات التقرير كقاموس
        """
        if not self.config:
            return {}

        try:
            return json.loads(self.config)
        except:
            return {}

class ReportField(models.Model):
    """
    نموذج حقل التقرير
    """
    report = models.ForeignKey(Report, on_delete=models.CASCADE, related_name="fields", verbose_name="التقرير")
    field = models.ForeignKey(Field, on_delete=models.CASCADE, related_name="report_fields", verbose_name="الحقل")
    label = models.CharField(max_length=100, blank=True, verbose_name="عنوان العرض")
    order = models.PositiveIntegerField(default=0, verbose_name="الترتيب")
    is_visible = models.BooleanField(default=True, verbose_name="ظاهر")
    is_groupable = models.BooleanField(default=False, verbose_name="قابل للتجميع")
    is_filterable = models.BooleanField(default=True, verbose_name="قابل للتصفية")
    aggregation = models.CharField(max_length=20, blank=True, verbose_name="دالة التجميع")

    class Meta:
        verbose_name = "حقل تقرير"
        verbose_name_plural = "حقول تقارير"
        ordering = ['report', 'order']
        unique_together = ('report', 'field')

    def __str__(self):
        return f"{self.label or self.field.label} ({self.report.name})"

class ReportFilter(models.Model):
    """
    نموذج فلتر التقرير
    """
    FILTER_OPERATORS = (
        ('equals', 'يساوي'),
        ('not_equals', 'لا يساوي'),
        ('contains', 'يحتوي على'),
        ('not_contains', 'لا يحتوي على'),
        ('starts_with', 'يبدأ بـ'),
        ('ends_with', 'ينتهي بـ'),
        ('greater_than', 'أكبر من'),
        ('less_than', 'أصغر من'),
        ('between', 'بين'),
        ('in_list', 'ضمن القائمة'),
        ('not_in_list', 'ليس ضمن القائمة'),
        ('is_null', 'فارغ'),
        ('is_not_null', 'غير فارغ'),
    )

    report = models.ForeignKey(Report, on_delete=models.CASCADE, related_name="filters", verbose_name="التقرير")
    field = models.ForeignKey(Field, on_delete=models.CASCADE, related_name="report_filters", verbose_name="الحقل")
    operator = models.CharField(max_length=20, choices=FILTER_OPERATORS, verbose_name="العملية")
    value = models.TextField(blank=True, null=True, verbose_name="القيمة")
    value2 = models.TextField(blank=True, null=True, verbose_name="القيمة الثانية")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "فلتر تقرير"
        verbose_name_plural = "فلاتر تقارير"

    def __str__(self):
        return f"{self.field.label} {self.get_operator_display()} ({self.report.name})"
