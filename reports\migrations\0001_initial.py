# Generated by Django 4.2.20 on 2025-05-12 07:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('tables', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('departments', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم التقرير')),
                ('description', models.TextField(blank=True, verbose_name='وصف التقرير')),
                ('report_type', models.CharField(choices=[('table', 'جدولي'), ('chart', 'رسم بياني'), ('summary', 'ملخص'), ('custom', 'مخصص')], default='table', max_length=20, verbose_name='نوع التقرير')),
                ('chart_type', models.CharField(blank=True, choices=[('bar', 'شريطي'), ('line', 'خطي'), ('pie', 'دائري'), ('area', 'مساحي'), ('scatter', 'نقطي'), ('radar', 'راداري'), ('heatmap', 'خريطة حرارية')], max_length=20, null=True, verbose_name='نوع الرسم البياني')),
                ('is_public', models.BooleanField(default=False, verbose_name='عام')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('config', models.TextField(blank=True, null=True, verbose_name='إعدادات التقرير')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_reports', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='departments.department', verbose_name='القسم')),
                ('table', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reports', to='tables.table', verbose_name='الجدول')),
            ],
            options={
                'verbose_name': 'تقرير',
                'verbose_name_plural': 'تقارير',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReportFilter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operator', models.CharField(choices=[('equals', 'يساوي'), ('not_equals', 'لا يساوي'), ('contains', 'يحتوي على'), ('not_contains', 'لا يحتوي على'), ('starts_with', 'يبدأ بـ'), ('ends_with', 'ينتهي بـ'), ('greater_than', 'أكبر من'), ('less_than', 'أصغر من'), ('between', 'بين'), ('in_list', 'ضمن القائمة'), ('not_in_list', 'ليس ضمن القائمة'), ('is_null', 'فارغ'), ('is_not_null', 'غير فارغ')], max_length=20, verbose_name='العملية')),
                ('value', models.TextField(blank=True, null=True, verbose_name='القيمة')),
                ('value2', models.TextField(blank=True, null=True, verbose_name='القيمة الثانية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_filters', to='tables.field', verbose_name='الحقل')),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='filters', to='reports.report', verbose_name='التقرير')),
            ],
            options={
                'verbose_name': 'فلتر تقرير',
                'verbose_name_plural': 'فلاتر تقارير',
            },
        ),
        migrations.CreateModel(
            name='ReportField',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('label', models.CharField(blank=True, max_length=100, verbose_name='عنوان العرض')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('is_visible', models.BooleanField(default=True, verbose_name='ظاهر')),
                ('is_groupable', models.BooleanField(default=False, verbose_name='قابل للتجميع')),
                ('is_filterable', models.BooleanField(default=True, verbose_name='قابل للتصفية')),
                ('aggregation', models.CharField(blank=True, max_length=20, verbose_name='دالة التجميع')),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='report_fields', to='tables.field', verbose_name='الحقل')),
                ('report', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fields', to='reports.report', verbose_name='التقرير')),
            ],
            options={
                'verbose_name': 'حقل تقرير',
                'verbose_name_plural': 'حقول تقارير',
                'ordering': ['report', 'order'],
                'unique_together': {('report', 'field')},
            },
        ),
    ]
