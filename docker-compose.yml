services:
  web:
    build: .
    restart: always
    volumes:
      - .:/app
    ports:
      - "0.0.0.0:8000:8000"
    environment:
      - DEBUG=True
      - SECRET_KEY=django-insecure-!y^#pxdn6rv8y1uspd!k9)6*(s_qhjv&75p!7cci7@s2w-q1wi
      - POSTGRES_DB=legal_management
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - DB_HOST=db
      - DB_PORT=5432
    command: >
      sh -c "python manage.py migrate &&
             python manage.py runserver 0.0.0.0:8000"
    depends_on:
      - db

  db:
    image: postgres:15-alpine
    restart: always
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_DB=legal_management

volumes:
  postgres_data:
