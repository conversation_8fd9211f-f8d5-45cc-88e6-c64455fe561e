from django import forms
from django.contrib.auth.models import User, Group
from .models import Department, DepartmentAdmin

class DepartmentForm(forms.ModelForm):
    """
    نموذج إنشاء وتعديل القسم
    """
    class Meta:
        model = Department
        fields = ['name', 'description', 'icon', 'color', 'order', 'is_active', 'is_public']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل اسم القسم'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'أدخل وصف القسم'}),
            'icon': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'مثال: fas fa-folder'}),
            'color': forms.TextInput(attrs={'class': 'form-control color-picker', 'placeholder': 'مثال: #0d6efd'}),
            'order': forms.NumberInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_public': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class DepartmentPermissionsForm(forms.ModelForm):
    """
    نموذج إدارة صلاحيات القسم
    """
    allowed_users = forms.ModelMultipleChoiceField(
        queryset=User.objects.filter(is_active=True),
        required=False,
        widget=forms.SelectMultiple(attrs={'class': 'form-control select2'}),
        label="المستخدمون المسموح لهم"
    )
    
    allowed_groups = forms.ModelMultipleChoiceField(
        queryset=Group.objects.all(),
        required=False,
        widget=forms.SelectMultiple(attrs={'class': 'form-control select2'}),
        label="المجموعات المسموح لها"
    )
    
    class Meta:
        model = Department
        fields = ['is_public', 'allowed_users', 'allowed_groups']
        widgets = {
            'is_public': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class DepartmentAdminForm(forms.ModelForm):
    """
    نموذج إضافة مدير للقسم
    """
    user = forms.ModelChoiceField(
        queryset=User.objects.filter(is_active=True),
        widget=forms.Select(attrs={'class': 'form-control select2'}),
        label="المستخدم"
    )
    
    class Meta:
        model = DepartmentAdmin
        fields = ['user', 'is_main_admin']
        widgets = {
            'is_main_admin': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
