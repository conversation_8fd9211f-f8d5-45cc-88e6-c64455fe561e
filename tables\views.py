from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db import transaction
from .models import Table, Field, TableRelation
from .forms import TableForm, FieldForm, TableRelationForm, DataImportForm, DataExportForm
from .dynamic_models import DynamicModelManager
from departments.models import Department
import json
import csv
import io
import datetime

@login_required
def table_list(request):
    """
    عرض قائمة الجداول
    """
    # الحصول على الجداول المتاحة للمستخدم الحالي
    if request.user.is_superuser:
        tables = Table.objects.all()
    else:
        # الجداول في الأقسام المتاحة للمستخدم
        departments = Department.objects.filter(
            is_active=True
        ).filter(
            is_public=True
        ) | Department.objects.filter(
            created_by=request.user
        ) | Department.objects.filter(
            allowed_users=request.user
        ) | Department.objects.filter(
            allowed_groups__in=request.user.groups.all()
        ).distinct()

        tables = Table.objects.filter(department__in=departments, is_active=True)

    context = {
        'tables': tables
    }

    return render(request, 'tables/list.html', context)

@login_required
def table_detail(request, table_id):
    """
    عرض تفاصيل الجدول
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الوصول
    if not table.department.user_has_access(request.user):
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذا الجدول")
        return redirect('tables:list')

    # الحصول على حقول الجدول
    fields = Field.objects.filter(table=table, is_active=True).order_by('order', 'name')

    # الحصول على علاقات الجدول
    relations = TableRelation.objects.filter(source_table=table)

    context = {
        'table': table,
        'fields': fields,
        'relations': relations,
        'department': table.department,
        'is_admin': request.user.is_superuser or table.department.created_by == request.user
    }

    return render(request, 'tables/detail.html', context)

@login_required
def table_create(request, department_id):
    """
    إنشاء جدول جديد
    """
    department = get_object_or_404(Department, id=department_id)

    # التحقق من صلاحية الإنشاء
    if not (request.user.is_superuser or department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لإنشاء جدول جديد في هذا القسم")
        return redirect('departments:detail', department_id=department.id)

    if request.method == 'POST':
        form = TableForm(request.POST)
        if form.is_valid():
            # إنشاء الجدول بدون استخدام transaction.atomic()
            table = form.save(commit=False)
            table.department = department
            table.created_by = request.user
            table.save()

            # إنشاء الجدول في قاعدة البيانات
            DynamicModelManager.create_table_in_database(table)

            messages.success(request, f"تم إنشاء الجدول '{table.name}' بنجاح")
            return redirect('tables:detail', table_id=table.id)
    else:
        form = TableForm()

    context = {
        'form': form,
        'department': department,
        'title': f"إنشاء جدول جديد في قسم: {department.name}"
    }

    return render(request, 'tables/form.html', context)

@login_required
def table_edit(request, table_id):
    """
    تعديل جدول
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية التعديل
    if not (request.user.is_superuser or table.department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لتعديل هذا الجدول")
        return redirect('tables:detail', table_id=table.id)

    if request.method == 'POST':
        form = TableForm(request.POST, instance=table)
        if form.is_valid():
            form.save()
            messages.success(request, f"تم تعديل الجدول '{table.name}' بنجاح")
            return redirect('tables:detail', table_id=table.id)
    else:
        form = TableForm(instance=table)

    context = {
        'form': form,
        'table': table,
        'department': table.department,
        'title': f"تعديل الجدول: {table.name}"
    }

    return render(request, 'tables/form.html', context)

@login_required
def table_delete(request, table_id):
    """
    حذف جدول
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الحذف
    if not (request.user.is_superuser or table.department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لحذف هذا الجدول")
        return redirect('tables:detail', table_id=table.id)

    if request.method == 'POST':
        department_id = table.department.id
        table_name = table.name

        # حذف الجدول من قاعدة البيانات بدون استخدام transaction.atomic()
        DynamicModelManager.drop_table_from_database(table)

        # حذف الجدول من النظام
        table.delete()

        messages.success(request, f"تم حذف الجدول '{table_name}' بنجاح")
        return redirect('departments:detail', department_id=department_id)

    context = {
        'table': table,
        'department': table.department
    }

    return render(request, 'tables/delete.html', context)

@login_required
def field_list(request, table_id):
    """
    عرض قائمة حقول الجدول
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الوصول
    if not table.department.user_has_access(request.user):
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذا الجدول")
        return redirect('tables:list')

    # الحصول على حقول الجدول
    fields = Field.objects.filter(table=table).order_by('order', 'name')

    context = {
        'table': table,
        'fields': fields,
        'department': table.department,
        'is_admin': request.user.is_superuser or table.department.created_by == request.user
    }

    return render(request, 'tables/field_list.html', context)

@login_required
def field_create(request, table_id):
    """
    إنشاء حقل جديد
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الإنشاء
    if not (request.user.is_superuser or table.department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لإنشاء حقل جديد في هذا الجدول")
        return redirect('tables:field_list', table_id=table.id)

    if request.method == 'POST':
        form = FieldForm(request.POST)
        if form.is_valid():
            # طباعة معلومات تشخيصية
            import sys
            print(f"إنشاء حقل جديد في الجدول {table.name} ({table.db_table_name})", file=sys.stderr)

            # التحقق من وجود حقل بنفس الاسم
            field_name = form.cleaned_data['name']
            if table.fields.filter(name=field_name).exists():
                messages.error(request, f"يوجد حقل بنفس الاسم '{field_name}' في الجدول")
                return redirect('tables:field_create', table_id=table.id)

            # إنشاء الحقل بدون استخدام transaction.atomic()
            field = form.save(commit=False)
            field.table = table
            field.save()

            print(f"تم إنشاء الحقل في النظام: {field.name} ({field.label})", file=sys.stderr)

            # إضافة الحقل إلى الجدول في قاعدة البيانات
            try:
                DynamicModelManager.add_field_to_table(field)
                print(f"تم إضافة الحقل {field.name} إلى الجدول {table.db_table_name} في قاعدة البيانات", file=sys.stderr)

                # التحقق من إضافة الحقل بنجاح
                from django.db import connection
                with connection.cursor() as cursor:
                    try:
                        cursor.execute(f"SELECT {field.name} FROM {table.db_table_name} LIMIT 1")
                        print(f"تم التحقق من إضافة الحقل {field.name} بنجاح", file=sys.stderr)
                    except Exception as e:
                        print(f"فشل التحقق من إضافة الحقل {field.name}: {str(e)}", file=sys.stderr)
                        messages.error(request, f"تم إنشاء الحقل في النظام ولكن فشل إضافته إلى قاعدة البيانات: {str(e)}")
                        # حذف الحقل من النظام
                        field.delete()
                        return redirect('tables:field_list', table_id=table.id)

                messages.success(request, f"تم إنشاء الحقل '{field.label}' بنجاح")
            except Exception as e:
                print(f"خطأ في إضافة الحقل {field.name} إلى الجدول {table.db_table_name}: {str(e)}", file=sys.stderr)
                messages.error(request, f"خطأ في إضافة الحقل إلى قاعدة البيانات: {str(e)}")
                # حذف الحقل من النظام
                field.delete()
                return redirect('tables:field_list', table_id=table.id)

            return redirect('tables:field_list', table_id=table.id)
    else:
        form = FieldForm()

    context = {
        'form': form,
        'table': table,
        'department': table.department,
        'title': f"إنشاء حقل جديد في جدول: {table.name}"
    }

    return render(request, 'tables/field_form.html', context)

@login_required
def field_edit(request, table_id, field_id):
    """
    تعديل حقل
    """
    table = get_object_or_404(Table, id=table_id)
    field = get_object_or_404(Field, id=field_id, table=table)

    # التحقق من صلاحية التعديل
    if not (request.user.is_superuser or table.department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لتعديل هذا الحقل")
        return redirect('tables:field_list', table_id=table.id)

    if request.method == 'POST':
        form = FieldForm(request.POST, instance=field)
        if form.is_valid():
            form.save()
            messages.success(request, f"تم تعديل الحقل '{field.label}' بنجاح")
            return redirect('tables:field_list', table_id=table.id)
    else:
        form = FieldForm(instance=field)

    context = {
        'form': form,
        'table': table,
        'field': field,
        'department': table.department,
        'title': f"تعديل الحقل: {field.label}"
    }

    return render(request, 'tables/field_form.html', context)

@login_required
def field_delete(request, table_id, field_id):
    """
    حذف حقل
    """
    table = get_object_or_404(Table, id=table_id)
    field = get_object_or_404(Field, id=field_id, table=table)

    # التحقق من صلاحية الحذف
    if not (request.user.is_superuser or table.department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لحذف هذا الحقل")
        return redirect('tables:field_list', table_id=table.id)

    if request.method == 'POST':
        field_label = field.label

        # حذف الحقل من الجدول في قاعدة البيانات بدون استخدام transaction.atomic()
        DynamicModelManager.remove_field_from_table(field)

        # حذف الحقل من النظام
        field.delete()

        messages.success(request, f"تم حذف الحقل '{field_label}' بنجاح")
        return redirect('tables:field_list', table_id=table.id)

    context = {
        'table': table,
        'field': field,
        'department': table.department
    }

    return render(request, 'tables/field_delete.html', context)

@login_required
def relation_list(request, table_id):
    """
    عرض قائمة علاقات الجدول
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الوصول
    if not table.department.user_has_access(request.user):
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذا الجدول")
        return redirect('tables:list')

    # الحصول على علاقات الجدول
    relations = TableRelation.objects.filter(source_table=table)

    context = {
        'table': table,
        'relations': relations,
        'department': table.department,
        'is_admin': request.user.is_superuser or table.department.created_by == request.user
    }

    return render(request, 'tables/relation_list.html', context)

@login_required
def relation_create(request, table_id):
    """
    إنشاء علاقة جديدة
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الإنشاء
    if not (request.user.is_superuser or table.department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لإنشاء علاقة جديدة لهذا الجدول")
        return redirect('tables:relation_list', table_id=table.id)

    # الحصول على الجداول المتاحة للعلاقة
    available_tables = Table.objects.filter(department=table.department, is_active=True).exclude(id=table.id)

    # الحصول على الحقول المتاحة للعلاقة
    available_fields = Field.objects.filter(table=table, is_active=True, field_type='relation')

    if request.method == 'POST':
        form = TableRelationForm(request.POST)
        if form.is_valid():
            relation = form.save(commit=False)
            relation.source_table = table
            relation.save()

            messages.success(request, f"تم إنشاء العلاقة '{relation.name}' بنجاح")
            return redirect('tables:relation_list', table_id=table.id)
    else:
        form = TableRelationForm()

        # تعيين الخيارات المتاحة
        form.fields['target_table'].queryset = available_tables
        form.fields['source_field'].queryset = available_fields

    context = {
        'form': form,
        'table': table,
        'department': table.department,
        'title': f"إنشاء علاقة جديدة لجدول: {table.name}"
    }

    return render(request, 'tables/relation_form.html', context)

@login_required
def relation_edit(request, table_id, relation_id):
    """
    تعديل علاقة
    """
    table = get_object_or_404(Table, id=table_id)
    relation = get_object_or_404(TableRelation, id=relation_id, source_table=table)

    # التحقق من صلاحية التعديل
    if not (request.user.is_superuser or table.department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لتعديل هذه العلاقة")
        return redirect('tables:relation_list', table_id=table.id)

    # الحصول على الجداول المتاحة للعلاقة
    available_tables = Table.objects.filter(department=table.department, is_active=True).exclude(id=table.id)

    # الحصول على الحقول المتاحة للعلاقة
    available_fields = Field.objects.filter(table=table, is_active=True, field_type='relation')

    if request.method == 'POST':
        form = TableRelationForm(request.POST, instance=relation)
        if form.is_valid():
            form.save()
            messages.success(request, f"تم تعديل العلاقة '{relation.name}' بنجاح")
            return redirect('tables:relation_list', table_id=table.id)
    else:
        form = TableRelationForm(instance=relation)

        # تعيين الخيارات المتاحة
        form.fields['target_table'].queryset = available_tables
        form.fields['source_field'].queryset = available_fields

    context = {
        'form': form,
        'table': table,
        'relation': relation,
        'department': table.department,
        'title': f"تعديل العلاقة: {relation.name}"
    }

    return render(request, 'tables/relation_form.html', context)

@login_required
def relation_delete(request, table_id, relation_id):
    """
    حذف علاقة
    """
    table = get_object_or_404(Table, id=table_id)
    relation = get_object_or_404(TableRelation, id=relation_id, source_table=table)

    # التحقق من صلاحية الحذف
    if not (request.user.is_superuser or table.department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لحذف هذه العلاقة")
        return redirect('tables:relation_list', table_id=table.id)

    if request.method == 'POST':
        relation_name = relation.name
        relation.delete()

        messages.success(request, f"تم حذف العلاقة '{relation_name}' بنجاح")
        return redirect('tables:relation_list', table_id=table.id)

    context = {
        'table': table,
        'relation': relation,
        'department': table.department
    }

    return render(request, 'tables/relation_delete.html', context)

@login_required
def fix_table(request, table_id):
    """
    إصلاح جدول محدد
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الوصول
    if not (request.user.is_superuser or table.department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لإصلاح هذا الجدول")
        return redirect('tables:list')

    # إعادة بناء الجدول
    success = DynamicModelManager.rebuild_table(table)

    if success:
        messages.success(request, f"تم إعادة بناء الجدول {table.name} بنجاح")
    else:
        messages.error(request, f"فشل في إعادة بناء الجدول {table.name}")

    return redirect('tables:data_list', table_id=table.id)

@login_required
def data_list(request, table_id):
    """
    عرض قائمة بيانات الجدول
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الوصول
    if not table.department.user_has_access(request.user):
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذا الجدول")
        return redirect('tables:list')

    # الحصول على حقول الجدول
    fields = Field.objects.filter(table=table, is_active=True).order_by('order', 'name')

    # طباعة معلومات الحقول
    print(f"عدد الحقول: {fields.count()}")
    for field in fields:
        print(f"الحقل: {field.name}, النوع: {field.field_type}, الاسم المعروض: {field.label}")

    try:
        # التحقق من تطابق الحقول قبل عرض البيانات
        print("التحقق من تطابق الحقول قبل عرض البيانات...")
        fields_verified = DynamicModelManager.verify_table_fields(table)

        if not fields_verified:
            print("تم اكتشاف عدم تطابق في الحقول وتم إصلاحه")
    except Exception as e:
        # في حالة حدوث خطأ، نعرض رسالة للمستخدم ونقدم خيار إصلاح الجدول
        print(f"خطأ أثناء التحقق من تطابق الحقول: {str(e)}")
        messages.error(request, f"حدث خطأ في الجدول: {str(e)}. يمكنك محاولة إصلاح الجدول.")
        context = {
            'table': table,
            'department': table.department,
            'error': str(e),
            'fields': fields
        }
        return render(request, 'tables/table_error.html', context)

    # الحصول على النموذج الديناميكي للجدول
    model = DynamicModelManager.get_model_for_table(table)

    # طباعة معلومات النموذج
    print(f"اسم النموذج: {model.__name__}")
    print(f"حقول النموذج: {[f.name for f in model._meta.fields]}")
    print(f"اسم الجدول في قاعدة البيانات: {model._meta.db_table}")
    print(f"اسم الجدول في النظام: {table.db_table_name}")

    # التحقق من تطابق اسم الجدول في النموذج مع اسم الجدول في النظام
    if model._meta.db_table != table.db_table_name:
        print(f"تحذير: اسم الجدول في النموذج ({model._meta.db_table}) لا يتطابق مع اسم الجدول في النظام ({table.db_table_name})")

        # تنظيف النموذج من ذاكرة التطبيق وإعادة إنشائه
        DynamicModelManager.clean_model_from_cache(table)
        model = DynamicModelManager.get_model_for_table(table)
        print(f"تم إعادة إنشاء النموذج. اسم الجدول الجديد: {model._meta.db_table}")

    # تطبيق التصفية والفرز
    filter_params = {}
    search_query = request.GET.get('q', '')

    if search_query:
        # تطبيق البحث على الحقول القابلة للبحث
        search_fields = fields.filter(is_searchable=True)

        if search_fields.exists():
            from django.db.models import Q
            query = Q()

            for field in search_fields:
                if field.field_type in ['text', 'textarea', 'html', 'email', 'phone', 'url', 'select', 'radio']:
                    query |= Q(**{f"{field.name}__icontains": search_query})

            records = model.objects.filter(query)
        else:
            records = model.objects.all()
    else:
        records = model.objects.all()

    # تطبيق الفرز
    sort_field = request.GET.get('sort', 'id')
    sort_dir = request.GET.get('dir', 'asc')

    if sort_dir == 'desc':
        sort_field = f"-{sort_field}"

    records = records.order_by(sort_field)

    # طباعة معلومات السجلات
    print(f"عدد السجلات قبل التقسيم: {records.count()}")
    for i, record in enumerate(records[:5]):  # طباعة أول 5 سجلات فقط
        print(f"السجل {i+1}:")
        for field in fields:
            try:
                value = getattr(record, field.name)
                print(f"  {field.name} ({field.field_type}): {value}")
            except Exception as e:
                print(f"  خطأ في الحصول على قيمة {field.name}: {str(e)}")

    # التحقق من وجود السجلات في قاعدة البيانات مباشرة
    from django.db import connection
    with connection.cursor() as cursor:
        try:
            cursor.execute(f"SELECT * FROM {table.db_table_name} LIMIT 5")
            rows = cursor.fetchall()
            print(f"السجلات من قاعدة البيانات مباشرة ({table.db_table_name}):")
            for row in rows:
                print(f"  {row}")
        except Exception as e:
            print(f"خطأ في استعلام قاعدة البيانات: {str(e)}")

    # التقسيم إلى صفحات
    from django.core.paginator import Paginator
    paginator = Paginator(records, 20)  # 20 سجل في كل صفحة
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)

    context = {
        'table': table,
        'fields': fields,
        'records': page_obj,
        'department': table.department,
        'is_admin': request.user.is_superuser or table.department.created_by == request.user,
        'search_query': search_query,
        'sort_field': sort_field.replace('-', ''),
        'sort_dir': sort_dir
    }

    return render(request, 'tables/data_list.html', context)

@login_required
def data_create(request, table_id):
    """
    إنشاء سجل جديد
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الإنشاء
    if not table.department.user_has_access(request.user):
        messages.error(request, "ليس لديك صلاحية لإضافة بيانات إلى هذا الجدول")
        return redirect('tables:data_list', table_id=table.id)

    # الحصول على حقول الجدول
    fields = Field.objects.filter(table=table, is_active=True).order_by('order', 'name')

    # الحصول على النموذج الديناميكي للجدول
    model = DynamicModelManager.get_model_for_table(table)

    if request.method == 'POST':
        # إنشاء نموذج ديناميكي للبيانات
        from django import forms

        form_fields = {}
        for field in fields:
            field_class = DynamicModelManager.get_field_class(field.field_type)
            field_kwargs = DynamicModelManager.get_field_kwargs(field)

            # تحويل الحقل إلى حقل نموذج
            if field.field_type == 'text':
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    max_length=255,
                    widget=forms.TextInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'textarea':
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
                )
            elif field.field_type == 'html':
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.Textarea(attrs={'class': 'form-control html-editor'})
                )
            elif field.field_type == 'email':
                form_fields[field.name] = forms.EmailField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.EmailInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'integer':
                form_fields[field.name] = forms.IntegerField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.NumberInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'decimal':
                form_fields[field.name] = forms.DecimalField(
                    label=field.label,
                    required=field.is_required,
                    max_digits=15,
                    decimal_places=2,
                    widget=forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'})
                )
            elif field.field_type == 'date':
                form_fields[field.name] = forms.DateField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
                )
            elif field.field_type == 'boolean':
                form_fields[field.name] = forms.BooleanField(
                    label=field.label,
                    required=False,
                    widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
                )
            elif field.field_type in ['select', 'radio']:
                choices = []
                if field.options:
                    try:
                        options = json.loads(field.options)
                        choices = [(option['value'], option['label']) for option in options]
                    except:
                        choices = [(option.strip(), option.strip()) for option in field.options.split(',') if option.strip()]

                form_fields[field.name] = forms.ChoiceField(
                    label=field.label,
                    required=field.is_required,
                    choices=[('', '-- اختر --')] + choices,
                    widget=forms.Select(attrs={'class': 'form-control'}) if field.field_type == 'select' else forms.RadioSelect(attrs={'class': 'form-check-input'})
                )
            # يمكن إضافة المزيد من أنواع الحقول حسب الحاجة

        # إنشاء النموذج الديناميكي
        DynamicForm = type('DynamicForm', (forms.Form,), form_fields)
        form = DynamicForm(request.POST)

        if form.is_valid():
            # إنشاء سجل جديد
            record = model()

            # تعيين قيم الحقول
            print("القيم المدخلة:")
            for field_name, field_value in form.cleaned_data.items():
                print(f"الحقل: {field_name}, القيمة: {field_value}")
                setattr(record, field_name, field_value)

            # تعيين المستخدم الذي أنشأ السجل
            record.created_by = request.user

            # طباعة قيم السجل قبل الحفظ
            print("قيم السجل قبل الحفظ:")
            for field in fields:
                print(f"الحقل: {field.name}, القيمة: {getattr(record, field.name, None)}")

            # طباعة معلومات النموذج والجدول
            print(f"اسم النموذج: {model.__name__}")
            print(f"اسم الجدول في قاعدة البيانات: {model._meta.db_table}")
            print(f"اسم الجدول في النظام: {table.db_table_name}")

            # التحقق من تطابق اسم الجدول في النموذج مع اسم الجدول في النظام
            if model._meta.db_table != table.db_table_name:
                print(f"تحذير: اسم الجدول في النموذج ({model._meta.db_table}) لا يتطابق مع اسم الجدول في النظام ({table.db_table_name})")

                # تنظيف النموذج من ذاكرة التطبيق وإعادة إنشائه
                DynamicModelManager.clean_model_from_cache(table)
                model = DynamicModelManager.get_model_for_table(table)
                print(f"تم إعادة إنشاء النموذج. اسم الجدول الجديد: {model._meta.db_table}")

                # إعادة إنشاء السجل باستخدام النموذج الجديد
                record = model()
                for field_name, field_value in form.cleaned_data.items():
                    setattr(record, field_name, field_value)
                record.created_by = request.user

            try:
                # حفظ السجل
                record.save()

                # طباعة قيم السجل بعد الحفظ
                print("قيم السجل بعد الحفظ:")
                print(f"معرف السجل: {record.id}")
                for field in fields:
                    print(f"الحقل: {field.name}, القيمة: {getattr(record, field.name, None)}")

                # التحقق من وجود السجل في قاعدة البيانات مباشرة
                from django.db import connection
                with connection.cursor() as cursor:
                    try:
                        cursor.execute(f"SELECT * FROM {table.db_table_name} WHERE id = {record.id}")
                        row = cursor.fetchone()
                        print(f"السجل من قاعدة البيانات مباشرة:")
                        print(f"  {row}")
                    except Exception as e:
                        print(f"خطأ في استعلام قاعدة البيانات: {str(e)}")

                messages.success(request, "تم إنشاء السجل بنجاح")
                return redirect('tables:data_list', table_id=table.id)
            except Exception as e:
                print(f"خطأ أثناء حفظ السجل: {str(e)}")
                messages.error(request, f"حدث خطأ أثناء حفظ السجل: {str(e)}")
                # عرض الخطأ في الصفحة
                form.add_error(None, f"خطأ في حفظ البيانات: {str(e)}")
    else:
        # إنشاء نموذج فارغ
        from django import forms

        form_fields = {}
        for field in fields:
            # تحويل الحقل إلى حقل نموذج
            if field.field_type == 'text':
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    max_length=255,
                    widget=forms.TextInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'textarea':
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
                )
            elif field.field_type == 'integer':
                form_fields[field.name] = forms.IntegerField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.NumberInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'date':
                form_fields[field.name] = forms.DateField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
                )
            elif field.field_type == 'boolean':
                form_fields[field.name] = forms.BooleanField(
                    label=field.label,
                    required=False,
                    widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
                )
            else:
                # للأنواع الأخرى، استخدم حقل نص عام
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.TextInput(attrs={'class': 'form-control'})
                )

        # إنشاء النموذج الديناميكي
        DynamicForm = type('DynamicForm', (forms.Form,), form_fields)
        form = DynamicForm()

    context = {
        'form': form,
        'table': table,
        'fields': fields,
        'department': table.department,
        'title': f"إضافة سجل جديد إلى جدول: {table.name}"
    }

    return render(request, 'tables/data_form.html', context)

@login_required
def data_detail(request, table_id, record_id):
    """
    عرض تفاصيل سجل
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الوصول
    if not table.department.user_has_access(request.user):
        messages.error(request, "ليس لديك صلاحية للوصول إلى هذا الجدول")
        return redirect('tables:list')

    # الحصول على حقول الجدول
    fields = Field.objects.filter(table=table, is_active=True).order_by('order', 'name')

    # الحصول على النموذج الديناميكي للجدول
    model = DynamicModelManager.get_model_for_table(table)

    # الحصول على السجل
    record = get_object_or_404(model, id=record_id)

    context = {
        'table': table,
        'fields': fields,
        'record': record,
        'department': table.department,
        'is_admin': request.user.is_superuser or table.department.created_by == request.user
    }

    return render(request, 'tables/data_detail.html', context)

@login_required
def data_edit(request, table_id, record_id):
    """
    تعديل سجل
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية التعديل
    if not table.department.user_has_access(request.user):
        messages.error(request, "ليس لديك صلاحية لتعديل بيانات هذا الجدول")
        return redirect('tables:data_list', table_id=table.id)

    # الحصول على حقول الجدول
    fields = Field.objects.filter(table=table, is_active=True).order_by('order', 'name')

    # الحصول على النموذج الديناميكي للجدول
    model = DynamicModelManager.get_model_for_table(table)

    # الحصول على السجل
    record = get_object_or_404(model, id=record_id)

    if request.method == 'POST':
        # إنشاء نموذج ديناميكي للبيانات
        from django import forms

        form_fields = {}
        for field in fields:
            # تحويل الحقل إلى حقل نموذج
            if field.field_type == 'text':
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    max_length=255,
                    widget=forms.TextInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'textarea':
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
                )
            elif field.field_type == 'integer':
                form_fields[field.name] = forms.IntegerField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.NumberInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'date':
                form_fields[field.name] = forms.DateField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
                )
            elif field.field_type == 'boolean':
                form_fields[field.name] = forms.BooleanField(
                    label=field.label,
                    required=False,
                    widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
                )
            else:
                # للأنواع الأخرى، استخدم حقل نص عام
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.TextInput(attrs={'class': 'form-control'})
                )

        # إنشاء النموذج الديناميكي
        DynamicForm = type('DynamicForm', (forms.Form,), form_fields)
        form = DynamicForm(request.POST)

        if form.is_valid():
            # تحديث قيم السجل
            for field_name, field_value in form.cleaned_data.items():
                setattr(record, field_name, field_value)

            # حفظ السجل
            record.save()

            messages.success(request, "تم تحديث السجل بنجاح")
            return redirect('tables:data_detail', table_id=table.id, record_id=record.id)
    else:
        # إنشاء نموذج معبأ بالبيانات الحالية
        from django import forms

        form_fields = {}
        initial_data = {}

        for field in fields:
            # تحويل الحقل إلى حقل نموذج
            if field.field_type == 'text':
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    max_length=255,
                    widget=forms.TextInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'textarea':
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 3})
                )
            elif field.field_type == 'integer':
                form_fields[field.name] = forms.IntegerField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.NumberInput(attrs={'class': 'form-control'})
                )
            elif field.field_type == 'date':
                form_fields[field.name] = forms.DateField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.DateInput(attrs={'class': 'form-control', 'type': 'date'})
                )
            elif field.field_type == 'boolean':
                form_fields[field.name] = forms.BooleanField(
                    label=field.label,
                    required=False,
                    widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
                )
            else:
                # للأنواع الأخرى، استخدم حقل نص عام
                form_fields[field.name] = forms.CharField(
                    label=field.label,
                    required=field.is_required,
                    widget=forms.TextInput(attrs={'class': 'form-control'})
                )

            # تعيين القيمة الأولية
            initial_data[field.name] = getattr(record, field.name, None)

        # إنشاء النموذج الديناميكي
        DynamicForm = type('DynamicForm', (forms.Form,), form_fields)
        form = DynamicForm(initial=initial_data)

    context = {
        'form': form,
        'table': table,
        'record': record,
        'fields': fields,
        'department': table.department,
        'title': f"تعديل سجل في جدول: {table.name}"
    }

    return render(request, 'tables/data_form.html', context)

@login_required
def data_delete(request, table_id, record_id):
    """
    حذف سجل
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الحذف
    if not table.department.user_has_access(request.user):
        messages.error(request, "ليس لديك صلاحية لحذف بيانات من هذا الجدول")
        return redirect('tables:data_list', table_id=table.id)

    # الحصول على النموذج الديناميكي للجدول
    model = DynamicModelManager.get_model_for_table(table)

    # الحصول على السجل
    record = get_object_or_404(model, id=record_id)

    if request.method == 'POST':
        record.delete()
        messages.success(request, "تم حذف السجل بنجاح")
        return redirect('tables:data_list', table_id=table.id)

    context = {
        'table': table,
        'record': record,
        'department': table.department
    }

    return render(request, 'tables/data_delete.html', context)

@login_required
def data_import(request, table_id):
    """
    استيراد بيانات إلى الجدول
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية الاستيراد
    if not (request.user.is_superuser or table.department.created_by == request.user):
        messages.error(request, "ليس لديك صلاحية لاستيراد بيانات إلى هذا الجدول")
        return redirect('tables:data_list', table_id=table.id)

    # الحصول على حقول الجدول
    fields = Field.objects.filter(table=table, is_active=True).order_by('order', 'name')

    # الحصول على النموذج الديناميكي للجدول
    model = DynamicModelManager.get_model_for_table(table)

    if request.method == 'POST':
        form = DataImportForm(request.POST, request.FILES)
        if form.is_valid():
            file = request.FILES['file']
            file_type = form.cleaned_data['file_type']
            has_header = form.cleaned_data['has_header']
            encoding = form.cleaned_data['encoding']

            # قراءة الملف
            if file_type == 'csv':
                # قراءة ملف CSV
                csv_data = file.read().decode(encoding)
                csv_reader = csv.reader(io.StringIO(csv_data))
                rows = list(csv_reader)
            elif file_type == 'excel':
                # قراءة ملف Excel
                import pandas as pd

                # طباعة معلومات تشخيصية
                print(f"استيراد ملف إكسل: {file.name}")

                # قراءة ملف الإكسل مع تحديد ترميز النص
                try:
                    # محاولة قراءة الملف مع ترميز UTF-8
                    df = pd.read_excel(file, engine='openpyxl')

                    # طباعة معلومات الإطار البياني
                    print(f"أبعاد الإطار البياني: {df.shape}")
                    print(f"أعمدة الإطار البياني: {df.columns.tolist()}")
                    print(f"أنواع البيانات: {df.dtypes}")
                    print(f"أول 5 صفوف من الإطار البياني:")
                    print(df.head())

                    # التحقق من وجود بيانات
                    if df.empty:
                        print("تحذير: الإطار البياني فارغ")

                    # التحقق من وجود قيم NaN
                    nan_count = df.isna().sum().sum()
                    print(f"عدد القيم NaN: {nan_count}")

                    # التحقق من وجود أعمدة فارغة
                    empty_cols = [col for col in df.columns if df[col].isna().all()]
                    if empty_cols:
                        print(f"الأعمدة الفارغة: {empty_cols}")

                    # التحقق من وجود صفوف فارغة
                    empty_rows = df.index[df.isna().all(axis=1)].tolist()
                    if empty_rows:
                        print(f"الصفوف الفارغة: {empty_rows}")

                    # طباعة قيم العمود الأول
                    if not df.empty and len(df.columns) > 0:
                        first_col = df.columns[0]
                        print(f"قيم العمود الأول ({first_col}):")
                        for i, val in enumerate(df[first_col]):
                            print(f"  الصف {i+1}: {val} (النوع: {type(val)})")
                except Exception as e:
                    print(f"خطأ في قراءة ملف الإكسل: {str(e)}")
                    # محاولة قراءة الملف بطريقة أخرى
                    try:
                        df = pd.read_excel(file, engine='xlrd')
                        print("تم قراءة الملف باستخدام محرك xlrd")
                    except Exception as e2:
                        print(f"خطأ في قراءة ملف الإكسل باستخدام محرك xlrd: {str(e2)}")
                        messages.error(request, f"خطأ في قراءة ملف الإكسل: {str(e)}")
                        return redirect('tables:data_import', table_id=table.id)

                # تحويل الإطار البياني إلى قائمة
                # تحديد العناوين
                headers = df.columns.tolist()
                print(f"العناوين الأصلية: {headers}")

                # معالجة العناوين
                processed_headers = []
                for header in headers:
                    # إذا كان العنوان رقمًا، قم بتحويله إلى نص
                    if isinstance(header, (int, float)):
                        header = str(header)
                    # إذا كان العنوان فارغًا، استخدم اسمًا افتراضيًا
                    if pd.isna(header) or header == '' or header is None:
                        header = f"Column{len(processed_headers)+1}"
                    processed_headers.append(header)

                print(f"العناوين بعد المعالجة: {processed_headers}")

                # إنشاء قائمة الصفوف
                rows = [processed_headers]

                # معالجة القيم NaN وتحويل البيانات
                for idx, row in df.iterrows():
                    # استبدال القيم NaN بسلاسل فارغة
                    processed_row = []
                    for i, val in enumerate(row):
                        # معالجة القيم الفارغة
                        if pd.isna(val):
                            processed_row.append('')
                        # معالجة القيم النصية
                        elif isinstance(val, str):
                            # تنظيف النص
                            processed_val = val.strip()
                            processed_row.append(processed_val)
                        # معالجة القيم الرقمية
                        elif isinstance(val, (int, float)):
                            processed_row.append(val)
                        # معالجة أنواع أخرى
                        else:
                            processed_row.append(str(val))

                    rows.append(processed_row)
                    print(f"الصف {idx+1} بعد المعالجة: {processed_row}")

                print(f"عدد الصفوف بعد المعالجة: {len(rows)}")
                print(f"أول صف (العناوين): {rows[0]}")
                if len(rows) > 1:
                    print(f"ثاني صف (أول سجل): {rows[1]}")
            elif file_type == 'json':
                # قراءة ملف JSON
                json_data = json.loads(file.read().decode(encoding))
                if isinstance(json_data, list):
                    if json_data and isinstance(json_data[0], dict):
                        # الحصول على أسماء الحقول من السجل الأول
                        headers = list(json_data[0].keys())
                        rows = [headers] + [[record.get(header, '') for header in headers] for record in json_data]
                    else:
                        messages.error(request, "تنسيق ملف JSON غير صالح")
                        return redirect('tables:data_import', table_id=table.id)
                else:
                    messages.error(request, "تنسيق ملف JSON غير صالح")
                    return redirect('tables:data_import', table_id=table.id)

            # تحديد الصف الأول (العناوين)
            headers = rows[0] if has_header else [f"Column{i+1}" for i in range(len(rows[0]))]

            # طباعة العناوين
            print(f"العناوين المستوردة: {headers}")
            print(f"حقول الجدول: {[f'{field.name} ({field.label})' for field in fields]}")

            # تعيين الحقول
            field_mapping = {}

            # إذا كان هناك عمود واحد فقط وهو يحتوي على أسماء، فقد يكون هذا هو حقل الاسم
            if len(headers) == 1 and headers[0].lower().strip() in ['name', 'الاسم', 'first_test1']:
                # البحث عن حقل الاسم
                name_field = None
                for field in fields:
                    if field.name.lower() in ['name', 'الاسم'] or field.label.lower() in ['name', 'الاسم']:
                        name_field = field
                        break

                if name_field:
                    field_mapping[0] = name_field.name
                    print(f"تم تعيين العمود الوحيد '{headers[0]}' إلى حقل الاسم '{name_field.name}'")
            else:
                # المطابقة العادية للحقول
                for i, header in enumerate(headers):
                    # البحث عن حقل مطابق
                    matching_field = None

                    # تنظيف العنوان للمقارنة
                    header_clean = str(header).lower().strip()

                    # محاولة المطابقة المباشرة أولاً
                    for field in fields:
                        field_name_clean = field.name.lower().strip()
                        field_label_clean = field.label.lower().strip()

                        if field_name_clean == header_clean or field_label_clean == header_clean:
                            matching_field = field
                            print(f"تم مطابقة العنوان '{header}' مع الحقل '{field.name} ({field.label})' (مطابقة مباشرة)")
                            break

                    # إذا لم يتم العثور على مطابقة مباشرة، حاول المطابقة الجزئية
                    if not matching_field:
                        for field in fields:
                            field_name_clean = field.name.lower().strip()
                            field_label_clean = field.label.lower().strip()

                            if header_clean in field_name_clean or header_clean in field_label_clean or field_name_clean in header_clean or field_label_clean in header_clean:
                                matching_field = field
                                print(f"تم مطابقة العنوان '{header}' مع الحقل '{field.name} ({field.label})' (مطابقة جزئية)")
                                break

                    # إذا لم يتم العثور على مطابقة، حاول المطابقة بناءً على الموقع
                    if not matching_field and i < len(fields):
                        matching_field = fields[i]
                        print(f"تم مطابقة العنوان '{header}' مع الحقل '{matching_field.name} ({matching_field.label})' (مطابقة بناءً على الموقع)")

                    if matching_field:
                        field_mapping[i] = matching_field.name
                    else:
                        print(f"تحذير: لم يتم العثور على حقل مطابق للعنوان '{header}'")

            # التعامل مع الحالة الخاصة: إذا كان هناك عمود واحد فقط بدون عنوان
            if len(headers) == 1 and (headers[0] == '' or headers[0] is None or pd.isna(headers[0])):
                # البحث عن حقل الاسم
                name_field = None
                for field in fields:
                    if field.name.lower() in ['name', 'الاسم'] or field.label.lower() in ['name', 'الاسم']:
                        name_field = field
                        break

                if name_field:
                    field_mapping[0] = name_field.name
                    print(f"تم تعيين العمود الوحيد بدون عنوان إلى حقل الاسم '{name_field.name}'")

            # التعامل مع الحالة الخاصة: إذا كان هناك عمود واحد فقط في الإطار البياني
            if df.shape[1] == 1 and len(field_mapping) == 0:
                # البحث عن حقل الاسم
                name_field = None
                for field in fields:
                    if field.name.lower() in ['name', 'الاسم'] or field.label.lower() in ['name', 'الاسم']:
                        name_field = field
                        break

                if name_field:
                    field_mapping[0] = name_field.name
                    print(f"تم تعيين العمود الوحيد في الإطار البياني إلى حقل الاسم '{name_field.name}'")

            # طباعة تعيين الحقول النهائي
            print(f"تعيين الحقول النهائي: {field_mapping}")

            # إذا لم يتم تعيين أي حقول، أضف رسالة خطأ
            if not field_mapping:
                messages.error(request, "لم يتم تعيين أي حقول. تأكد من أن أسماء الأعمدة في ملف الإكسل تتطابق مع أسماء الحقول في الجدول.")
                return redirect('tables:data_import', table_id=table.id)

            # استيراد البيانات
            imported_count = 0
            error_count = 0

            # طباعة معلومات النموذج والجدول
            print(f"اسم النموذج: {model.__name__}")
            print(f"اسم الجدول في قاعدة البيانات: {model._meta.db_table}")
            print(f"اسم الجدول في النظام: {table.db_table_name}")

            # التحقق من تطابق اسم الجدول في النموذج مع اسم الجدول في النظام
            if model._meta.db_table != table.db_table_name:
                print(f"تحذير: اسم الجدول في النموذج ({model._meta.db_table}) لا يتطابق مع اسم الجدول في النظام ({table.db_table_name})")

                # تنظيف النموذج من ذاكرة التطبيق وإعادة إنشائه
                DynamicModelManager.clean_model_from_cache(table)
                model = DynamicModelManager.get_model_for_table(table)
                print(f"تم إعادة إنشاء النموذج. اسم الجدول الجديد: {model._meta.db_table}")

            with transaction.atomic():
                for row_index, row in enumerate(rows):
                    if has_header and row_index == 0:
                        continue

                    # طباعة معلومات الصف
                    print(f"معالجة الصف {row_index}:")
                    for col_index, value in enumerate(row):
                        if col_index in field_mapping:
                            field_name = field_mapping[col_index]
                            print(f"  العمود {col_index} ({field_name}): {value}")

                    record = model()
                    record.created_by = request.user

                    # تعيين قيم الحقول
                    for col_index, value in enumerate(row):
                        if col_index in field_mapping:
                            field_name = field_mapping[col_index]

                            # الحصول على معلومات الحقل
                            field = next((f for f in fields if f.name == field_name), None)
                            if not field:
                                print(f"  تحذير: لم يتم العثور على معلومات الحقل {field_name}")
                                continue

                            print(f"  معالجة الحقل {field_name} (النوع: {field.field_type})")

                            # معالجة القيم الفارغة
                            if value is None or (isinstance(value, str) and value.strip() == ''):
                                print(f"  تحذير: القيمة فارغة للحقل {field_name}")
                                # تعيين قيمة فارغة بدلاً من تخطي الحقل
                                if field.field_type in ['text', 'textarea', 'email', 'url', 'phone', 'file']:
                                    setattr(record, field_name, '')
                                elif field.field_type in ['integer', 'decimal']:
                                    # لا تعين قيمة للحقول الرقمية إذا كانت فارغة
                                    pass
                                elif field.field_type == 'boolean':
                                    setattr(record, field_name, False)
                                elif field.field_type == 'date':
                                    # لا تعين قيمة للتاريخ إذا كان فارغًا
                                    pass
                                continue

                            # معالجة أنواع البيانات المختلفة
                            try:
                                if field.field_type == 'text' or field.field_type == 'textarea':
                                    # تأكد من أن القيمة نصية
                                    processed_value = str(value).strip()
                                    setattr(record, field_name, processed_value)
                                    print(f"    تم تعيين القيمة النصية: {processed_value}")

                                elif field.field_type == 'integer':
                                    # تحويل إلى عدد صحيح
                                    if isinstance(value, (int, float)):
                                        processed_value = int(value)
                                    else:
                                        processed_value = int(str(value).strip())
                                    setattr(record, field_name, processed_value)
                                    print(f"    تم تعيين القيمة العددية: {processed_value}")

                                elif field.field_type == 'decimal':
                                    # تحويل إلى عدد عشري
                                    if isinstance(value, (int, float)):
                                        processed_value = float(value)
                                    else:
                                        processed_value = float(str(value).strip())
                                    setattr(record, field_name, processed_value)
                                    print(f"    تم تعيين القيمة العشرية: {processed_value}")

                                elif field.field_type == 'boolean':
                                    # تحويل إلى قيمة منطقية
                                    if isinstance(value, bool):
                                        processed_value = value
                                    elif isinstance(value, (int, float)):
                                        processed_value = bool(value)
                                    elif isinstance(value, str):
                                        value_lower = value.lower().strip()
                                        processed_value = value_lower in ['true', 'yes', '1', 'نعم', 'صحيح']
                                    else:
                                        processed_value = bool(value)
                                    setattr(record, field_name, processed_value)
                                    print(f"    تم تعيين القيمة المنطقية: {processed_value}")

                                elif field.field_type == 'date':
                                    # تحويل إلى تاريخ
                                    import datetime
                                    if isinstance(value, datetime.date):
                                        processed_value = value
                                    elif isinstance(value, datetime.datetime):
                                        processed_value = value.date()
                                    elif isinstance(value, str):
                                        # محاولة تحليل التاريخ من النص
                                        from dateutil import parser
                                        processed_value = parser.parse(value).date()
                                    else:
                                        # إذا لم يمكن تحويل القيمة إلى تاريخ، استخدم التاريخ الحالي
                                        processed_value = datetime.date.today()
                                    setattr(record, field_name, processed_value)
                                    print(f"    تم تعيين قيمة التاريخ: {processed_value}")

                                else:
                                    # لأنواع الحقول الأخرى، استخدم القيمة كما هي
                                    setattr(record, field_name, value)
                                    print(f"    تم تعيين القيمة: {value}")

                            except Exception as e:
                                print(f"    خطأ في معالجة القيمة '{value}' للحقل {field_name}: {str(e)}")
                                # في حالة الخطأ، حاول تعيين القيمة كما هي
                                try:
                                    setattr(record, field_name, value)
                                    print(f"    تم تعيين القيمة بدون معالجة: {value}")
                                except Exception as e2:
                                    print(f"    فشل في تعيين القيمة: {str(e2)}")

                    # طباعة قيم السجل قبل الحفظ
                    print("  قيم السجل قبل الحفظ:")
                    for field in fields:
                        print(f"    {field.name}: {getattr(record, field.name, None)}")

                    try:
                        record.save()
                        imported_count += 1
                        print(f"  تم حفظ السجل بنجاح (معرف: {record.id})")
                    except Exception as e:
                        error_count += 1
                        print(f"  خطأ في حفظ السجل: {str(e)}")

            messages.success(request, f"تم استيراد {imported_count} سجل بنجاح. {error_count} سجل فشل في الاستيراد.")
            return redirect('tables:data_list', table_id=table.id)
    else:
        form = DataImportForm()

    context = {
        'form': form,
        'table': table,
        'fields': fields,
        'department': table.department,
        'title': f"استيراد بيانات إلى جدول: {table.name}"
    }

    return render(request, 'tables/data_import.html', context)

@login_required
def data_export(request, table_id):
    """
    تصدير بيانات من الجدول
    """
    table = get_object_or_404(Table, id=table_id)

    # التحقق من صلاحية التصدير
    if not table.department.user_has_access(request.user):
        messages.error(request, "ليس لديك صلاحية لتصدير بيانات من هذا الجدول")
        return redirect('tables:data_list', table_id=table.id)

    # الحصول على حقول الجدول
    fields = Field.objects.filter(table=table, is_active=True).order_by('order', 'name')

    # الحصول على النموذج الديناميكي للجدول
    model = DynamicModelManager.get_model_for_table(table)

    if request.method == 'POST':
        form = DataExportForm(request.POST)
        if form.is_valid():
            file_type = form.cleaned_data['file_type']
            include_header = form.cleaned_data['include_header']
            encoding = form.cleaned_data['encoding']

            # الحصول على البيانات
            records = model.objects.all()

            # تحضير البيانات للتصدير
            headers = [field.label for field in fields]
            data = []

            for record in records:
                row = [getattr(record, field.name, '') for field in fields]
                data.append(row)

            # تصدير البيانات
            if file_type == 'csv':
                # تصدير إلى CSV
                response = HttpResponse(content_type='text/csv')
                response['Content-Disposition'] = f'attachment; filename="{table.name}.csv"'

                writer = csv.writer(response)
                if include_header:
                    writer.writerow(headers)
                writer.writerows(data)

                return response
            elif file_type == 'excel':
                # تصدير إلى Excel
                import pandas as pd
                from io import BytesIO

                df = pd.DataFrame(data, columns=headers if include_header else None)

                output = BytesIO()
                with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                    df.to_excel(writer, index=False, sheet_name=table.name)

                output.seek(0)

                response = HttpResponse(output.read(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                response['Content-Disposition'] = f'attachment; filename="{table.name}.xlsx"'

                return response
            elif file_type == 'pdf':
                # تصدير إلى PDF
                from reportlab.lib import colors
                from reportlab.lib.pagesizes import letter, landscape
                from reportlab.platypus import SimpleDocTemplate, Table as PDFTable, TableStyle
                from io import BytesIO

                buffer = BytesIO()
                doc = SimpleDocTemplate(buffer, pagesize=landscape(letter))
                elements = []

                # إضافة البيانات
                table_data = [headers] if include_header else []
                table_data.extend(data)

                pdf_table = PDFTable(table_data)
                pdf_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ]))

                elements.append(pdf_table)
                doc.build(elements)

                response = HttpResponse(buffer.getvalue(), content_type='application/pdf')
                response['Content-Disposition'] = f'attachment; filename="{table.name}.pdf"'

                return response
            elif file_type == 'json':
                # تصدير إلى JSON
                json_data = []

                for record in records:
                    record_dict = {}
                    for field in fields:
                        record_dict[field.name] = getattr(record, field.name, '')
                    json_data.append(record_dict)

                response = HttpResponse(json.dumps(json_data, ensure_ascii=False, indent=4), content_type='application/json')
                response['Content-Disposition'] = f'attachment; filename="{table.name}.json"'

                return response
    else:
        form = DataExportForm()

    context = {
        'form': form,
        'table': table,
        'fields': fields,
        'department': table.department,
        'title': f"تصدير بيانات من جدول: {table.name}"
    }

    return render(request, 'tables/data_export.html', context)
