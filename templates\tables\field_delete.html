{% extends 'base.html' %}

{% block title %}حذف حقل {{ field.label }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:field_list' table.id %}">الحقول</a></li>
        <li class="breadcrumb-item active" aria-current="page">حذف {{ field.label }}</li>
    </ol>
</nav>

<div class="card border-danger">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">تأكيد الحذف</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> تحذير: سيؤدي حذف هذا الحقل إلى حذف جميع البيانات المرتبطة به بشكل دائم. هذا الإجراء لا يمكن التراجع عنه.
        </div>
        
        <p>هل أنت متأكد من رغبتك في حذف الحقل <strong>{{ field.label }}</strong> من جدول <strong>{{ table.name }}</strong>؟</p>
        
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">معلومات الحقل</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">اسم الحقل:</dt>
                    <dd class="col-sm-9">{{ field.name }}</dd>
                    
                    <dt class="col-sm-3">العنوان:</dt>
                    <dd class="col-sm-9">{{ field.label }}</dd>
                    
                    <dt class="col-sm-3">النوع:</dt>
                    <dd class="col-sm-9">{{ field.get_field_type_display }}</dd>
                    
                    <dt class="col-sm-3">الوصف:</dt>
                    <dd class="col-sm-9">{{ field.description|default:"لا يوجد وصف" }}</dd>
                    
                    <dt class="col-sm-3">مطلوب:</dt>
                    <dd class="col-sm-9">
                        {% if field.is_required %}
                        <span class="badge bg-success">نعم</span>
                        {% else %}
                        <span class="badge bg-secondary">لا</span>
                        {% endif %}
                    </dd>
                    
                    <dt class="col-sm-3">فريد:</dt>
                    <dd class="col-sm-9">
                        {% if field.is_unique %}
                        <span class="badge bg-success">نعم</span>
                        {% else %}
                        <span class="badge bg-secondary">لا</span>
                        {% endif %}
                    </dd>
                </dl>
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between">
                <a href="{% url 'tables:field_list' table.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> تأكيد الحذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
