# Generated by Django 4.2.20 on 2025-05-12 07:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('departments', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Table',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الجدول')),
                ('slug', models.SlugField(blank=True, max_length=120, verbose_name='الاسم اللطيف')),
                ('description', models.TextField(blank=True, verbose_name='وصف الجدول')),
                ('db_table_name', models.Char<PERSON>ield(max_length=100, unique=True, verbose_name='اسم الجدول في قاعدة البيانات')),
                ('icon', models.CharField(default='fas fa-table', max_length=50, verbose_name='أيقونة الجدول')),
                ('color', models.CharField(default='#0d6efd', max_length=20, verbose_name='لون الجدول')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('visibility', models.CharField(choices=[('public', 'عام'), ('private', 'خاص'), ('inherit', 'وراثة من القسم')], default='inherit', max_length=10, verbose_name='الرؤية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_tables', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tables', to='departments.department', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'جدول',
                'verbose_name_plural': 'جداول',
                'ordering': ['department', 'order', 'name'],
                'unique_together': {('department', 'slug')},
            },
        ),
        migrations.CreateModel(
            name='Field',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الحقل')),
                ('label', models.CharField(max_length=100, verbose_name='عنوان الحقل')),
                ('field_type', models.CharField(choices=[('text', 'نص قصير'), ('textarea', 'نص طويل'), ('html', 'نص منسق (HTML)'), ('email', 'بريد إلكتروني'), ('phone', 'رقم هاتف'), ('url', 'رابط URL'), ('integer', 'عدد صحيح'), ('decimal', 'عدد عشري'), ('currency', 'عملة'), ('percentage', 'نسبة مئوية'), ('date', 'تاريخ'), ('time', 'وقت'), ('datetime', 'تاريخ ووقت'), ('select', 'قائمة منسدلة'), ('multiselect', 'اختيار متعدد'), ('radio', 'زر راديو'), ('checkbox', 'صندوق اختيار'), ('file', 'ملف عام'), ('image', 'صورة'), ('pdf', 'ملف PDF'), ('document', 'مستند'), ('boolean', 'نعم/لا'), ('user', 'مستخدم'), ('relation', 'علاقة بجدول آخر')], max_length=20, verbose_name='نوع الحقل')),
                ('description', models.TextField(blank=True, verbose_name='وصف الحقل')),
                ('is_required', models.BooleanField(default=False, verbose_name='مطلوب')),
                ('is_unique', models.BooleanField(default=False, verbose_name='فريد')),
                ('is_indexed', models.BooleanField(default=False, verbose_name='مفهرس')),
                ('is_searchable', models.BooleanField(default=True, verbose_name='قابل للبحث')),
                ('default_value', models.TextField(blank=True, null=True, verbose_name='القيمة الافتراضية')),
                ('placeholder', models.CharField(blank=True, max_length=200, verbose_name='نص توضيحي')),
                ('help_text', models.CharField(blank=True, max_length=200, verbose_name='نص المساعدة')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('options', models.TextField(blank=True, null=True, verbose_name='خيارات الحقل')),
                ('validation_regex', models.CharField(blank=True, max_length=500, verbose_name='تعبير منتظم للتحقق')),
                ('min_value', models.CharField(blank=True, max_length=50, null=True, verbose_name='القيمة الدنيا')),
                ('max_value', models.CharField(blank=True, max_length=50, null=True, verbose_name='القيمة القصوى')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('table', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fields', to='tables.table', verbose_name='الجدول')),
            ],
            options={
                'verbose_name': 'حقل',
                'verbose_name_plural': 'حقول',
                'ordering': ['table', 'order', 'name'],
                'unique_together': {('table', 'name')},
            },
        ),
        migrations.CreateModel(
            name='TableRelation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('relation_type', models.CharField(choices=[('one_to_one', 'واحد لواحد'), ('one_to_many', 'واحد لمتعدد'), ('many_to_many', 'متعدد لمتعدد')], max_length=20, verbose_name='نوع العلاقة')),
                ('name', models.CharField(max_length=100, verbose_name='اسم العلاقة')),
                ('description', models.TextField(blank=True, verbose_name='وصف العلاقة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('source_field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_relations', to='tables.field', verbose_name='الحقل المصدر')),
                ('source_table', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_relations', to='tables.table', verbose_name='الجدول المصدر')),
                ('target_table', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_relations', to='tables.table', verbose_name='الجدول الهدف')),
            ],
            options={
                'verbose_name': 'علاقة بين الجداول',
                'verbose_name_plural': 'علاقات بين الجداول',
                'unique_together': {('source_table', 'target_table', 'source_field')},
            },
        ),
    ]
