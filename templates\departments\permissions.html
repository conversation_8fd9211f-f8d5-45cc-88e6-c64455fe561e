{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">إدارة الصلاحيات</li>
    </ol>
</nav>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ title }}</h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> يمكنك تحديد ما إذا كان هذا القسم عامًا (متاح للجميع) أو تحديد المستخدمين والمجموعات المسموح لهم بالوصول إليه.
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input type="checkbox" name="is_public" id="id_is_public" class="form-check-input {% if form.is_public.errors %}is-invalid{% endif %}" {% if form.is_public.value %}checked{% endif %}>
                            <label for="id_is_public" class="form-check-label">عام (متاح للجميع)</label>
                            {% if form.is_public.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.is_public.errors %}{{ error }}{% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="id_allowed_users" class="form-label">المستخدمون المسموح لهم</label>
                        <select name="allowed_users" id="id_allowed_users" class="form-control select2 {% if form.allowed_users.errors %}is-invalid{% endif %}" multiple>
                            {% for user in form.fields.allowed_users.queryset %}
                                <option value="{{ user.id }}" {% if user in form.allowed_users.value %}selected{% endif %}>{{ user.username }} ({{ user.get_full_name|default:user.username }})</option>
                            {% endfor %}
                        </select>
                        {% if form.allowed_users.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.allowed_users.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">اختر المستخدمين المسموح لهم بالوصول إلى هذا القسم</div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="id_allowed_groups" class="form-label">المجموعات المسموح لها</label>
                        <select name="allowed_groups" id="id_allowed_groups" class="form-control select2 {% if form.allowed_groups.errors %}is-invalid{% endif %}" multiple>
                            {% for group in form.fields.allowed_groups.queryset %}
                                <option value="{{ group.id }}" {% if group in form.allowed_groups.value %}selected{% endif %}>{{ group.name }}</option>
                            {% endfor %}
                        </select>
                        {% if form.allowed_groups.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.allowed_groups.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">اختر المجموعات المسموح لها بالوصول إلى هذا القسم</div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'departments:detail' department.id %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">مدراء القسم</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>اسم المستخدم</th>
                                <th>الاسم الكامل</th>
                                <th>مدير رئيسي</th>
                                <th>تاريخ الإضافة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if admins %}
                                {% for admin in admins %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ admin.user.username }}</td>
                                    <td>{{ admin.user.get_full_name|default:"-" }}</td>
                                    <td>
                                        {% if admin.is_main_admin %}
                                        <span class="badge bg-success">نعم</span>
                                        {% else %}
                                        <span class="badge bg-secondary">لا</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ admin.created_at|date:"Y-m-d" }}</td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="5" class="text-center">لا يوجد مدراء للقسم</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // تهيئة Select2
        $('.select2').select2({
            theme: 'bootstrap-5',
            dir: 'rtl',
            language: 'ar',
            placeholder: 'اختر...',
            allowClear: true
        });
        
        // تفعيل/تعطيل حقول الاختيار حسب حالة "عام"
        function toggleFields() {
            var isPublic = $('#id_is_public').is(':checked');
            if (isPublic) {
                $('#id_allowed_users').prop('disabled', true);
                $('#id_allowed_groups').prop('disabled', true);
            } else {
                $('#id_allowed_users').prop('disabled', false);
                $('#id_allowed_groups').prop('disabled', false);
            }
        }
        
        // تنفيذ الدالة عند تحميل الصفحة
        toggleFields();
        
        // تنفيذ الدالة عند تغيير حالة "عام"
        $('#id_is_public').change(function() {
            toggleFields();
        });
    });
</script>
{% endblock %}
