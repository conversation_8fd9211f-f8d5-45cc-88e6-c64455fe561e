{% extends 'base.html' %}

{% block title %}حقول {{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">الحقول</li>
    </ol>
</nav>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="mb-0">
        <i class="{{ table.icon }}" style="color: {{ table.color }}"></i>
        حقول {{ table.name }}
    </h1>
    
    <div class="btn-group">
        <a href="{% url 'tables:detail' table.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة إلى الجدول
        </a>
        {% if is_admin %}
        <a href="{% url 'tables:field_create' table.id %}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة حقل جديد
        </a>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if fields %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الحقل</th>
                        <th>العنوان</th>
                        <th>النوع</th>
                        <th>مطلوب</th>
                        <th>فريد</th>
                        <th>الترتيب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for field in fields %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ field.name }}</td>
                        <td>{{ field.label }}</td>
                        <td>{{ field.get_field_type_display }}</td>
                        <td>
                            {% if field.is_required %}
                            <span class="badge bg-success"><i class="fas fa-check"></i></span>
                            {% else %}
                            <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                            {% endif %}
                        </td>
                        <td>
                            {% if field.is_unique %}
                            <span class="badge bg-success"><i class="fas fa-check"></i></span>
                            {% else %}
                            <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                            {% endif %}
                        </td>
                        <td>{{ field.order }}</td>
                        <td>
                            {% if is_admin %}
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'tables:field_edit' table.id field.id %}" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'tables:field_delete' table.id field.id %}" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> لا توجد حقول في هذا الجدول.
            {% if is_admin %}
            <a href="{% url 'tables:field_create' table.id %}" class="alert-link">إضافة حقل جديد</a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<div class="card mt-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">معلومات الجدول</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <dl class="row">
                    <dt class="col-sm-4">اسم الجدول:</dt>
                    <dd class="col-sm-8">{{ table.name }}</dd>
                    
                    <dt class="col-sm-4">القسم:</dt>
                    <dd class="col-sm-8">{{ department.name }}</dd>
                    
                    <dt class="col-sm-4">الوصف:</dt>
                    <dd class="col-sm-8">{{ table.description|default:"لا يوجد وصف" }}</dd>
                </dl>
            </div>
            <div class="col-md-6">
                <dl class="row">
                    <dt class="col-sm-4">اسم الجدول في قاعدة البيانات:</dt>
                    <dd class="col-sm-8">{{ table.db_table_name }}</dd>
                    
                    <dt class="col-sm-4">تاريخ الإنشاء:</dt>
                    <dd class="col-sm-8">{{ table.created_at|date:"Y-m-d H:i" }}</dd>
                    
                    <dt class="col-sm-4">الحالة:</dt>
                    <dd class="col-sm-8">
                        {% if table.is_active %}
                        <span class="badge bg-success">نشط</span>
                        {% else %}
                        <span class="badge bg-danger">غير نشط</span>
                        {% endif %}
                    </dd>
                </dl>
            </div>
        </div>
    </div>
</div>
{% endblock %}
