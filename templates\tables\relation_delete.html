{% extends 'base.html' %}

{% block title %}حذف علاقة - {{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:relation_list' table.id %}">العلاقات</a></li>
        <li class="breadcrumb-item active" aria-current="page">حذف علاقة</li>
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-danger text-white">
        <h5 class="mb-0">حذف علاقة</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> هل أنت متأكد من رغبتك في حذف هذه العلاقة؟ هذا الإجراء لا يمكن التراجع عنه.
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">معلومات العلاقة</h6>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">الحقل المصدر:</dt>
                    <dd class="col-sm-9">{{ relation.source_field.label }}</dd>
                    
                    <dt class="col-sm-3">الجدول المستهدف:</dt>
                    <dd class="col-sm-9">{{ relation.target_table.name }}</dd>
                    
                    <dt class="col-sm-3">نوع العلاقة:</dt>
                    <dd class="col-sm-9">{{ relation.get_relation_type_display }}</dd>
                    
                    <dt class="col-sm-3">حقل العرض:</dt>
                    <dd class="col-sm-9">{{ relation.display_field.label }}</dd>
                    
                    <dt class="col-sm-3">الوصف:</dt>
                    <dd class="col-sm-9">{{ relation.description|default:"لا يوجد وصف" }}</dd>
                </dl>
            </div>
        </div>
        
        <form method="post">
            {% csrf_token %}
            <div class="d-flex justify-content-between">
                <a href="{% url 'tables:relation_list' table.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash"></i> تأكيد الحذف
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
