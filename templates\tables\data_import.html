{% extends 'base.html' %}

{% block title %}استيراد بيانات - {{ table.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:list' %}">الأقسام</a></li>
        <li class="breadcrumb-item"><a href="{% url 'departments:detail' department.id %}">{{ department.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:detail' table.id %}">{{ table.name }}</a></li>
        <li class="breadcrumb-item"><a href="{% url 'tables:data_list' table.id %}">البيانات</a></li>
        <li class="breadcrumb-item active" aria-current="page">استيراد بيانات</li>
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">استيراد بيانات إلى {{ table.name }}</h5>
    </div>
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> يمكنك استيراد البيانات من ملفات CSV أو Excel أو JSON. تأكد من أن الملف يحتوي على أعمدة تتطابق مع حقول الجدول.
        </div>
        
        <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
            {% csrf_token %}
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="id_file" class="form-label">الملف</label>
                    <input type="file" name="file" id="id_file" class="form-control {% if form.file.errors %}is-invalid{% endif %}" required>
                    {% if form.file.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.file.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-6">
                    <label for="id_file_type" class="form-label">نوع الملف</label>
                    <select name="file_type" id="id_file_type" class="form-select {% if form.file_type.errors %}is-invalid{% endif %}" required>
                        <option value="csv" {% if form.file_type.value == 'csv' %}selected{% endif %}>CSV</option>
                        <option value="excel" {% if form.file_type.value == 'excel' %}selected{% endif %}>Excel</option>
                        <option value="json" {% if form.file_type.value == 'json' %}selected{% endif %}>JSON</option>
                    </select>
                    {% if form.file_type.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.file_type.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="id_encoding" class="form-label">ترميز الملف</label>
                    <select name="encoding" id="id_encoding" class="form-select {% if form.encoding.errors %}is-invalid{% endif %}">
                        <option value="utf-8" {% if form.encoding.value == 'utf-8' %}selected{% endif %}>UTF-8</option>
                        <option value="utf-16" {% if form.encoding.value == 'utf-16' %}selected{% endif %}>UTF-16</option>
                        <option value="windows-1256" {% if form.encoding.value == 'windows-1256' %}selected{% endif %}>Windows-1256 (Arabic)</option>
                        <option value="iso-8859-6" {% if form.encoding.value == 'iso-8859-6' %}selected{% endif %}>ISO-8859-6 (Arabic)</option>
                    </select>
                    {% if form.encoding.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.encoding.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                </div>
                
                <div class="col-md-6">
                    <div class="form-check mt-4">
                        <input type="checkbox" name="has_header" id="id_has_header" class="form-check-input {% if form.has_header.errors %}is-invalid{% endif %}" {% if form.has_header.value %}checked{% endif %}>
                        <label for="id_has_header" class="form-check-label">الملف يحتوي على صف عناوين</label>
                        {% if form.has_header.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.has_header.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">حقول الجدول</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اسم الحقل</th>
                                    <th>العنوان</th>
                                    <th>النوع</th>
                                    <th>مطلوب</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for field in fields %}
                                <tr>
                                    <td>{{ field.name }}</td>
                                    <td>{{ field.label }}</td>
                                    <td>{{ field.get_field_type_display }}</td>
                                    <td>
                                        {% if field.is_required %}
                                        <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                        {% else %}
                                        <span class="badge bg-secondary"><i class="fas fa-times"></i></span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% url 'tables:data_list' table.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-file-import"></i> استيراد البيانات
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">تعليمات الاستيراد</h5>
    </div>
    <div class="card-body">
        <h6>تنسيق CSV</h6>
        <p>يجب أن يكون ملف CSV بالتنسيق التالي:</p>
        <pre class="bg-light p-3 rounded">{{ csv_example }}</pre>
        
        <h6 class="mt-4">تنسيق JSON</h6>
        <p>يجب أن يكون ملف JSON بالتنسيق التالي:</p>
        <pre class="bg-light p-3 rounded">{{ json_example }}</pre>
        
        <h6 class="mt-4">ملاحظات هامة</h6>
        <ul>
            <li>تأكد من أن أسماء الأعمدة في الملف تتطابق مع أسماء الحقول أو عناوينها في الجدول.</li>
            <li>الحقول المطلوبة يجب أن تحتوي على قيم في الملف المستورد.</li>
            <li>سيتم تجاهل الأعمدة التي لا تتطابق مع أي حقل في الجدول.</li>
            <li>إذا كان الملف يحتوي على بيانات غير صالحة، سيتم تجاهل السجلات المعنية وإظهار عدد السجلات التي فشل استيرادها.</li>
        </ul>
    </div>
</div>
{% endblock %}
