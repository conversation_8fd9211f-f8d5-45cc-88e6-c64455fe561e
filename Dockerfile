FROM python:3.11-slim

# تعيين متغيرات البيئة
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# تعيين دليل العمل
WORKDIR /app

# تثبيت متطلبات النظام
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# تثبيت المتطلبات
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# نسخ ملفات المشروع
COPY . /app/

# تشغيل أوامر التهيئة
RUN python manage.py collectstatic --noinput

# تعريض المنفذ
EXPOSE 8000

# أمر التشغيل
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
