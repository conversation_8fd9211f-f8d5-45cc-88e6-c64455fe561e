{% extends 'base_print.html' %}
{% load table_tags %}

{% block title %}طباعة تقرير - {{ report.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<div class="container mt-4 print-container">
    <div class="text-center mb-4">
        <h2>{{ report.name }}</h2>
        <p class="text-muted">{{ report.department.name }} - {{ report.table.name }}</p>
        <p class="small">تاريخ الطباعة: {% now "Y-m-d H:i" %}</p>
    </div>
    
    {% if report.description %}
    <div class="mb-4">
        <h5>وصف التقرير:</h5>
        <p>{{ report.description }}</p>
    </div>
    {% endif %}
    
    {% if report_filters %}
    <div class="mb-4">
        <h5>فلاتر التقرير:</h5>
        <table class="table table-sm table-bordered">
            <thead>
                <tr>
                    <th>الحقل</th>
                    <th>العملية</th>
                    <th>القيمة</th>
                </tr>
            </thead>
            <tbody>
                {% for filter in report_filters %}
                <tr>
                    <td>{{ filter.field.label }}</td>
                    <td>{{ filter.get_operator_display }}</td>
                    <td>
                        {% if filter.operator == 'between' %}
                        {{ filter.value }} - {{ filter.value2 }}
                        {% elif filter.operator == 'is_null' or filter.operator == 'is_not_null' %}
                        -
                        {% else %}
                        {{ filter.value }}
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
    
    {% if report.report_type == 'chart' and chart_data %}
    <div class="mb-4">
        <h5>الرسم البياني:</h5>
        <div class="chart-container">
            <canvas id="reportChart" width="800" height="400"></canvas>
        </div>
    </div>
    {% endif %}
    
    <div class="mb-4">
        <h5>بيانات التقرير:</h5>
        {% if records %}
        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        {% for field in report_fields %}
                        {% if field.is_visible %}
                        <th>{{ field.label|default:field.field.label }}</th>
                        {% endif %}
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for record in records %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        {% for field in report_fields %}
                        {% if field.is_visible %}
                        <td>
                            {% with value=record|getattribute:field.field.name %}
                            {% if field.field.field_type == 'boolean' %}
                                {% if value %}نعم{% else %}لا{% endif %}
                            {% elif field.field.field_type == 'date' %}
                                {{ value|date:"Y-m-d" }}
                            {% elif field.field.field_type == 'time' %}
                                {{ value|time:"H:i" }}
                            {% elif field.field.field_type == 'datetime' %}
                                {{ value|date:"Y-m-d H:i" }}
                            {% else %}
                                {{ value|default:"-" }}
                            {% endif %}
                            {% endwith %}
                        </td>
                        {% endif %}
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> لا توجد بيانات متاحة لهذا التقرير.
        </div>
        {% endif %}
    </div>
    
    <div class="footer text-center mt-4">
        <p class="small">تم إنشاء هذا التقرير بواسطة: {{ report.created_by.get_full_name|default:report.created_by.username }}</p>
        <p class="small">نظام إدارة الدائرة القانونية &copy; {% now "Y" %}</p>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    @media print {
        body {
            font-size: 12pt;
        }
        
        .print-container {
            width: 100%;
            max-width: 100%;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th, .table td {
            padding: 5px;
            border: 1px solid #ddd;
        }
        
        .footer {
            position: fixed;
            bottom: 0;
            width: 100%;
            text-align: center;
            font-size: 10pt;
            color: #666;
        }
        
        @page {
            size: A4;
            margin: 1cm;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
{% if report.report_type == 'chart' and chart_data %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var ctx = document.getElementById('reportChart').getContext('2d');
        var chartData = {{ chart_data|safe }};
        
        var myChart = new Chart(ctx, {
            type: chartData.type,
            data: {
                labels: chartData.labels,
                datasets: chartData.datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom'
                    }
                }
            }
        });
        
        // طباعة الصفحة تلقائيًا بعد تحميل الرسم البياني
        setTimeout(function() {
            window.print();
        }, 1000);
    });
</script>
{% else %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // طباعة الصفحة تلقائيًا
        setTimeout(function() {
            window.print();
        }, 500);
    });
</script>
{% endif %}
{% endblock %}
