{% extends 'base.html' %}
{% load table_tags %}

{% block title %}تصدير تقرير - {{ report.name }} - نظام إدارة الدائرة القانونية{% endblock %}

{% block content %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'home' %}">الرئيسية</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:list' %}">التقارير</a></li>
        <li class="breadcrumb-item"><a href="{% url 'reports:detail' report.id %}">{{ report.name }}</a></li>
        <li class="breadcrumb-item active" aria-current="page">تصدير</li>
    </ol>
</nav>

<div class="card">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">تصدير التقرير: {{ report.name }}</h5>
    </div>
    <div class="card-body">
        <form method="post" class="needs-validation" novalidate>
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                {{ error }}
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="{{ form.file_type.id_for_label }}" class="form-label">{{ form.file_type.label }}</label>
                    {{ form.file_type }}
                    {% if form.file_type.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.file_type.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                    {% if form.file_type.help_text %}
                    <small class="form-text text-muted">{{ form.file_type.help_text }}</small>
                    {% endif %}
                </div>
                
                <div class="col-md-6 mb-3">
                    <label for="{{ form.encoding.id_for_label }}" class="form-label">{{ form.encoding.label }}</label>
                    {{ form.encoding }}
                    {% if form.encoding.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.encoding.errors %}{{ error }}{% endfor %}
                    </div>
                    {% endif %}
                    {% if form.encoding.help_text %}
                    <small class="form-text text-muted">{{ form.encoding.help_text }}</small>
                    {% endif %}
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="form-check">
                        {{ form.include_header }}
                        <label for="{{ form.include_header.id_for_label }}" class="form-check-label">{{ form.include_header.label }}</label>
                        {% if form.include_header.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.include_header.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                        {% if form.include_header.help_text %}
                        <small class="form-text text-muted">{{ form.include_header.help_text }}</small>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6 mb-3">
                    <div class="form-check">
                        {{ form.include_filters }}
                        <label for="{{ form.include_filters.id_for_label }}" class="form-check-label">{{ form.include_filters.label }}</label>
                        {% if form.include_filters.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.include_filters.errors %}{{ error }}{% endfor %}
                        </div>
                        {% endif %}
                        {% if form.include_filters.help_text %}
                        <small class="form-text text-muted">{{ form.include_filters.help_text }}</small>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="card mb-3">
                <div class="card-header bg-light">
                    <h6 class="mb-0">الحقول المضمنة في التصدير</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم الحقل</th>
                                    <th>عنوان العرض</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for field in report_fields %}
                                <tr>
                                    <td>{{ forloop.counter }}</td>
                                    <td>{{ field.field.name }}</td>
                                    <td>{{ field.label|default:field.field.label }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-between">
                <a href="{% url 'reports:detail' report.id %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-file-export"></i> تصدير
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تطبيق الأنماط على حقول النموذج
        document.querySelectorAll('input, select, textarea').forEach(function(element) {
            if (element.type !== 'checkbox' && element.type !== 'radio') {
                element.classList.add('form-control');
                
                if (element.required) {
                    element.classList.add('is-required');
                }
                
                if (element.classList.contains('is-invalid')) {
                    element.classList.add('is-invalid');
                }
            } else {
                element.classList.add('form-check-input');
            }
            
            if (element.tagName === 'SELECT') {
                element.classList.add('form-select');
            }
        });
    });
</script>
{% endblock %}
