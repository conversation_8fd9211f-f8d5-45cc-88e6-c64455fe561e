from django import forms
from .models import Report, ReportField, ReportFilter
from tables.models import Table, Field

class ReportForm(forms.ModelForm):
    """
    نموذج إنشاء وتعديل التقرير
    """
    class Meta:
        model = Report
        fields = ['name', 'description', 'table', 'report_type', 'chart_type', 'is_public', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل اسم التقرير'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'أدخل وصف التقرير'}),
            'table': forms.Select(attrs={'class': 'form-control'}),
            'report_type': forms.Select(attrs={'class': 'form-control'}),
            'chart_type': forms.Select(attrs={'class': 'form-control'}),
            'is_public': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, department=None, **kwargs):
        super().__init__(*args, **kwargs)
        if department:
            # تصفية الجداول حسب القسم
            self.fields['table'].queryset = Table.objects.filter(department=department, is_active=True)

class ReportFieldForm(forms.ModelForm):
    """
    نموذج إضافة حقل للتقرير
    """
    class Meta:
        model = ReportField
        fields = ['field', 'label', 'order', 'is_visible', 'is_groupable', 'is_filterable', 'aggregation']
        widgets = {
            'field': forms.Select(attrs={'class': 'form-control'}),
            'label': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل عنوان العرض'}),
            'order': forms.NumberInput(attrs={'class': 'form-control'}),
            'is_visible': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_groupable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_filterable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'aggregation': forms.Select(attrs={'class': 'form-control'}, choices=[
                ('', 'بدون تجميع'),
                ('sum', 'المجموع'),
                ('avg', 'المتوسط'),
                ('min', 'الحد الأدنى'),
                ('max', 'الحد الأقصى'),
                ('count', 'العدد'),
            ]),
        }
    
    def __init__(self, *args, report=None, **kwargs):
        super().__init__(*args, **kwargs)
        if report:
            # تصفية الحقول حسب جدول التقرير
            self.fields['field'].queryset = Field.objects.filter(table=report.table, is_active=True)

class ReportFilterForm(forms.ModelForm):
    """
    نموذج إضافة فلتر للتقرير
    """
    class Meta:
        model = ReportFilter
        fields = ['field', 'operator', 'value', 'value2', 'is_active']
        widgets = {
            'field': forms.Select(attrs={'class': 'form-control'}),
            'operator': forms.Select(attrs={'class': 'form-control'}),
            'value': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل القيمة'}),
            'value2': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'أدخل القيمة الثانية'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, report=None, **kwargs):
        super().__init__(*args, **kwargs)
        if report:
            # تصفية الحقول حسب جدول التقرير
            self.fields['field'].queryset = Field.objects.filter(table=report.table, is_active=True)

class ReportExportForm(forms.Form):
    """
    نموذج تصدير التقرير
    """
    file_type = forms.ChoiceField(
        label="نوع الملف",
        choices=[
            ('pdf', 'PDF'),
            ('excel', 'Excel'),
            ('csv', 'CSV'),
            ('json', 'JSON'),
        ],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    include_header = forms.BooleanField(
        label="تضمين الترويسة",
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    include_filters = forms.BooleanField(
        label="تضمين معايير التصفية",
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    
    page_size = forms.ChoiceField(
        label="حجم الصفحة",
        choices=[
            ('a4', 'A4'),
            ('letter', 'Letter'),
            ('legal', 'Legal'),
        ],
        initial='a4',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    orientation = forms.ChoiceField(
        label="اتجاه الصفحة",
        choices=[
            ('portrait', 'عمودي'),
            ('landscape', 'أفقي'),
        ],
        initial='portrait',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
