/* 
 * نظام إدارة الدائرة القانونية - هيئة المنافذ الحدودية
 * الأنماط الرئيسية
 */

/* الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

body {
    font-family: 'Tajawal', sans-serif;
    background-color: #f8f9fa;
}

/* الشريط العلوي */
.navbar-brand {
    font-weight: 700;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.85);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #ffffff;
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: 1rem;
}

/* البطاقات */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
    border: none;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* الأزرار */
.btn {
    border-radius: 0.25rem;
    padding: 0.375rem 1rem;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* الجداول */
.table {
    background-color: #fff;
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* النماذج */
.form-control {
    border-radius: 0.25rem;
    padding: 0.5rem 0.75rem;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* الأقسام */
.department-card {
    transition: transform 0.2s;
}

.department-card:hover {
    transform: translateY(-5px);
}

.department-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* الجداول الديناميكية */
.dynamic-table-container {
    background-color: #fff;
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.dynamic-table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.dynamic-table-filters {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* التقارير */
.report-card {
    height: 100%;
}

.chart-container {
    height: 300px;
    margin-bottom: 1rem;
}

/* الصفحة الرئيسية */
.welcome-section {
    background-color: #e9ecef;
    border-radius: 0.5rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

/* تذييل الصفحة */
footer {
    margin-top: 3rem;
}

/* تخصيصات للشاشات الصغيرة */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1rem;
    }
    
    .welcome-section {
        padding: 1rem;
    }
    
    .chart-container {
        height: 200px;
    }
}
